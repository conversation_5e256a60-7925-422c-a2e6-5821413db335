{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["# Load environment variables and set up auto-reload\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"attachments": {"098d1586-3ed0-44f1-b265-b62758864ad4.webp": {"image/webp": "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"}, "4582386e-344c-487a-87ce-d37263f67dc9.webp": {"image/webp": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# Multi-Agent Research System\n", "\n", "This notebook combines all the previous components into a single system.\n", "\n", "Here is our overall research flow:\n", "\n", "![image.webp](attachment:098d1586-3ed0-44f1-b265-b62758864ad4.webp)\n", "\n", "We've built research scoping and multi-agent research in previous notebooks.\n", "\n", "Now, we'll add the final report generation step.\n", "\n", "![Screenshot_2025-07-10_at_4.12.03_PM.webp](attachment:4582386e-344c-487a-87ce-d37263f67dc9.webp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Agent \n", "\n", "We simply can re-use the components we've already built."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_research_from_scratch/research_agent_full.py\n"]}], "source": ["%%writefile ../src/deep_research_from_scratch/research_agent_full.py\n", "\n", "\"\"\"\n", "Full Multi-Agent Research System\n", "\n", "This module integrates all components of the research system:\n", "- User clarification and scoping\n", "- Research brief generation  \n", "- Multi-agent research coordination\n", "- Final report generation\n", "\n", "The system orchestrates the complete research workflow from initial user\n", "input through final report delivery.\n", "\"\"\"\n", "\n", "from langchain_core.messages import HumanMessage\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "from deep_research_from_scratch.utils import get_today_str\n", "from deep_research_from_scratch.prompts import final_report_generation_prompt\n", "from deep_research_from_scratch.state_scope import AgentState, AgentInputState\n", "from deep_research_from_scratch.research_agent_scope import clarify_with_user, write_research_brief\n", "from deep_research_from_scratch.multi_agent_supervisor import supervisor_agent\n", "\n", "# ===== Config =====\n", "\n", "from langchain.chat_models import init_chat_model\n", "writer_model = init_chat_model(model=\"openai:gpt-4.1\", max_tokens=32000) # model=\"anthropic:claude-sonnet-4-20250514\", max_tokens=64000\n", "\n", "# ===== FINAL REPORT GENERATION =====\n", "\n", "from deep_research_from_scratch.state_scope import AgentState\n", "\n", "async def final_report_generation(state: AgentState):\n", "    \"\"\"\n", "    Final report generation node.\n", "    \n", "    Synthesizes all research findings into a comprehensive final report\n", "    \"\"\"\n", "    \n", "    notes = state.get(\"notes\", [])\n", "    \n", "    findings = \"\\n\".join(notes)\n", "\n", "    final_report_prompt = final_report_generation_prompt.format(\n", "        research_brief=state.get(\"research_brief\", \"\"),\n", "        findings=findings,\n", "        date=get_today_str()\n", "    )\n", "    \n", "    final_report = await writer_model.ainvoke([HumanMessage(content=final_report_prompt)])\n", "    \n", "    return {\n", "        \"final_report\": final_report.content, \n", "        \"messages\": [\"Here is the final report: \" + final_report.content],\n", "    }\n", "\n", "# ===== GRAPH CONSTRUCTION =====\n", "# Build the overall workflow\n", "deep_researcher_builder = StateGraph(AgentState, input_schema=AgentInputState)\n", "\n", "# Add workflow nodes\n", "deep_researcher_builder.add_node(\"clarify_with_user\", clarify_with_user)\n", "deep_researcher_builder.add_node(\"write_research_brief\", write_research_brief)\n", "deep_researcher_builder.add_node(\"supervisor_subgraph\", supervisor_agent)\n", "deep_researcher_builder.add_node(\"final_report_generation\", final_report_generation)\n", "\n", "# Add workflow edges\n", "deep_researcher_builder.add_edge(START, \"clarify_with_user\")\n", "deep_researcher_builder.add_edge(\"write_research_brief\", \"supervisor_subgraph\")\n", "deep_researcher_builder.add_edge(\"supervisor_subgraph\", \"final_report_generation\")\n", "deep_researcher_builder.add_edge(\"final_report_generation\", END)\n", "\n", "# Compile the full workflow\n", "agent = deep_researcher_builder.compile()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Run the workflow\n", "from utils import format_messages\n", "from IPython.display import Image, display\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from deep_research_from_scratch.research_agent_full import deep_researcher_builder\n", "\n", "checkpointer = InMemorySaver()\n", "full_agent = deep_researcher_builder.compile(checkpointer=checkpointer)\n", "display(Image(full_agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LangGraph has a default recursion limit of 25 steps to prevent infinite loops. For complex research workflows that require iterative rounds of research, this limit needs to be increased. As explained in [LangGraph's troubleshooting guide](https://langchain-ai.github.io/langgraph/troubleshooting/errors/GRAPH_RECURSION_LIMIT/), the recursion limit counts every node execution in the graph. In our multi-agent research system:\n", "\n", "- **Single Research Agent**: May take 8-12 steps for tool calls and compression\n", "- **Multi-Agent Supervisor**: Each sub-agent spawned adds additional steps\n", "- **Iterative Research**: Supervisor may conduct multiple rounds of research to fill gaps\n", "- **Full Workflow**: Includes scoping, research brief generation, supervision, and report generation\n", "\n", "We set the recursion limit to **50** to accommodate:\n", "- Complex research topics requiring multiple research rounds\n", "- <PERSON>lle<PERSON> sub-agent execution\n", "- Deep research with many tool calls\n", "- Complete workflow execution from scoping to final report\n", "\n", "This allows the supervisor to conduct iterative rounds of research when initial findings have gaps, ensuring comprehensive coverage of complex research topics."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Compare Gemini to OpenAI Deep Research agents.                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Compare Gemini to OpenAI Deep Research agents.                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Could you clarify what you mean by 'OpenAI Deep Research agents'? Are you referring to a specific product,      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> research project, or a general class of AI agents developed by OpenAI?                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m Could you clarify what you mean by 'OpenAI Deep Research agents'? Are you referring to a specific product,      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m research project, or a general class of AI agents developed by OpenAI?                                          \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain_core.messages import HumanMessage\n", "thread = {\"configurable\": {\"thread_id\": \"1\", \"recursion_limit\": 50}}\n", "result = await full_agent.ainvoke({\"messages\": [HumanMessage(content=\"Compare Gemini to OpenAI Deep Research agents.\")]}, config=thread)\n", "format_messages(result['messages'])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["result = await full_agent.ainvoke({\"messages\": [HumanMessage(content=\"Yes the specific Deep Research products.\")]}, config=thread)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Compare Gemini to OpenAI Deep Research agents.                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Compare Gemini to OpenAI Deep Research agents.                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Could you clarify what you mean by 'OpenAI Deep Research agents'? Are you referring to a specific product,      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> research project, or a general class of AI agents developed by OpenAI?                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m Could you clarify what you mean by 'OpenAI Deep Research agents'? Are you referring to a specific product,      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m research project, or a general class of AI agents developed by OpenAI?                                          \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Yes the specific Deep Research products.                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Yes the specific Deep Research products.                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Thank you for clarifying that you are referring to the specific Deep Research products from OpenAI. I           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> understand that you would like a comparison between Gemini and OpenAI's Deep Research agents. I will now begin  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> the research and prepare a comparative analysis based on the information provided.                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m Thank you for clarifying that you are referring to the specific Deep Research products from OpenAI. I           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m understand that you would like a comparison between Gemini and OpenAI's Deep Research agents. I will now begin  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m the research and prepare a comparative analysis based on the information provided.                              \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Here is the final report: # Comprehensive Comparison: Google Gemini vs. OpenAI Deep Research Agents (2025)      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ## Introduction                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> This report provides a detailed, balanced comparison of Google’s Gemini AI suite and OpenAI’s Deep Research     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> agents. Both technologies represent state-of-the-art advancements in artificial intelligence but are designed   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> with distinct architectures, capabilities, use cases, and integration models. This analysis examines key        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> aspects such as core features, intended applications, underlying technologies, performance benchmarks,          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> integration options, and notable strengths and weaknesses, referencing the latest information and official      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> documentation as of August 2025.                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ---                                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ## Overview: Google Gemini                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Core Capabilities                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Multimodality**: Gemini supports native processing of text, images, video, audio, code, and PDF inputs, and <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> can generate outputs in text, audio, and structured data formats. Recent iterations, such as Gemini 2.5 Deep    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Think, can handle context windows exceeding 1 million tokens and output up to 192,000 tokens, enabling the      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> processing of vast and complex documents or media streams.                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Advanced Reasoning**: The “Deep Think” and Pro variants deploy parallel hypothesis testing and              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> reinforcement learning, excelling at coding, complex mathematical reasoning, multimodal understanding,          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> structured outputs, and function/tool calling.                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Agentic Capabilities**: Gemini powers research agents and universal AI assistants (e.g., Project Astra,     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Project Mariner), handling autonomous workflows like code debugging, real-time information retrieval, and       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> interactive web task completion.                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Safety and Governance**: Gemini employs extensive safety measures, including red teaming (automated and     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> external), multi-tiered abuse monitoring, mitigations against sensitive domains (e.g., chemical/biological),    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> and oversight by Google DeepMind’s Responsibility and Safety Council. It adheres to ethical principles such as  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> minimization of bias and toxicity.                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Intended Use Cases                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Complex knowledge work (e.g., scientific research, coding assistants, market analysis)                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Large-scale document analysis, summarization, and synthesis                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Content creation across text, image, video (e.g., AI-powered design, video generation with Veo, music with    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Lyria)                                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Autonomous agents for enterprise workflows (e.g., web research, spreadsheet automation, technical support,    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> creative design)                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Integration into Google products and services: Workspace (Docs, Sheets, Slides), Pixel devices, Search, Ads,  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Chrome, and Duet AI                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Underlying Technologies and Architecture                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Sparse Mixture-of-Experts (MoE) Transformers**: Efficiently routes token streams to specialist “experts,”   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> scaling model power and reducing unnecessary computation compared to dense models.                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Training Infrastructure**: Built and scaled atop Google’s custom Tensor Processing Units (TPUs), notably    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> the latest Trillium and v5p generations, offering high energy efficiency and throughput.                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Data and Multimodal Alignment**: Gemini models are instruction-tuned on diverse datasets spanning internet  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> text, code, multimodal data, and human tool-use demonstrations, with additional reinforcement from human and AI <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> feedback loops.                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Agentic Systems**: Gemini integrates tool use (code execution, search), rapid context expansion (handling   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> very large documents), and interaction with third-party tools through its APIs.                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Performance Benchmarks                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Academic and Coding Tasks**:                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Outperformed humans on MMLU (90.0% - Gemini Ultra) and competes at IMO (mathematics competition) Bronze     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> level (60.7%).                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Achieves strong results on coding (LiveCodeBench 87.6%), knowledge, and reasoning benchmarks.               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Agentic Tasks**:                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Project Mariner and Gemini Plays Pokémon showcase advanced planning, tool use, and context management,      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> e.g., completing Pokémon Blue autonomously after hundreds of hours.                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Comparative Edge**:                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Gemini is recognized for high factual accuracy, extremely large context windows, and rapid processing,      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> especially for multimodal or large-scale tasks.                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Integration Options                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **APIs**: Accessible via Google AI Studio, Vertex AI (for cloud deployment), and dedicated APIs (REST, SDKs   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> in various languages).                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Product Deep Integration**: Gemini is foundational to Workspace tools, Pixel on-device AI, Search Labs' AI  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Overviews, and Duet AI for enterprise productivity.                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Developer Ecosystem**: Offers Model Garden for testing/deployment, detailed rate limits with scalable       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> quotas, batch mode support for cost efficiency, and transparent, usage-based pricing.                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Fine-tuning &amp; Customization**: Support for model customization and domain-specific tasks, with facilities   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> for prompt engineering, tool chaining, and safe deployment monitoring.                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Notable Strengths and Weaknesses                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> **Strengths**                                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Broad and deep multimodality with vast context (1M+ tokens)                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - High-speed inference for large, complex workflows                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Deep integration across Google’s ecosystem and developer tools                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Industry-leading benchmarking on factual, reasoning, and coding tasks                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Advanced safety governance                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> **Weaknesses**                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Some known issues with hallucination and occasional latency, particularly at the highest computation tiers    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Over-refusal of benign queries due to conservative safety layers                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Earlier access to specialized features (e.g., newest model variants) may be gated or subject to usage         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> restrictions                                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Premium enterprise pricing for advanced models and batch processing                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> [1][2][3][4][5][6][7][8][9][10][11][12]                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ---                                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ## Overview: OpenAI Deep Research Agents                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Core Capabilities                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Autonomous Multi-Step Research**: Deep Research agents can independently plan, browse the real-time         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> internet, analyze hundreds of resources (websites, PDFs, images, structured files), synthesize findings, and    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> generate comprehensive, citation-backed research reports. Transparency is emphasized through step-by-step       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> action logs and in-line source attribution.                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Agentic Reasoning &amp; Workflow Automation**: Empowers users to delegate entire research projects, from web    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> data collection to report writing, planning, and visualization. The newly launched ChatGPT Agent combines these <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> capabilities with code execution, web automation, and third-party tool integration (e.g., creating              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> presentations or spreadsheets).                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Citations &amp; Analytical Traceability**: For every decisive step, Deep Research logs tools invoked (web       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> search, code interpreters, Model Context Protocol connectors), building a traceable reasoning chain akin to an  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> expert analyst’s workflow.                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Safety &amp; User Control**: Implements explicit user approval for consequential actions, privacy/sandboxing    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> for sensitive tasks, and comprehensive risk mitigations, including prompt-injection and data exfiltration       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> defenses.                                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Intended Use Cases                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - In-depth market, policy, technical, or literature research requiring source-backed synthesis                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Competitive intelligence, product comparison, and strategy reports                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Automated health economics analysis, regulatory compliance checks, and legal research                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Spreadsheet automation, technical reporting, real-time trend and news aggregation                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Enterprise integrations (Gmail, GitHub, financial modeling) and advanced agentic workflows                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Underlying Technologies and Architecture                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Large-scale Reasoning Models**: Deep Research leverages optimized variants of OpenAI’s o3 and forthcoming   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> o4 models, which are highly agentic, employing transformer backbones enhanced by real-world tool-use learning   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> and advanced browsing capabilities.                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Reinforcement Learning on Real Tasks**: Trained through reinforcement learning on real                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> browser/Python/coding tasks, with extensive multi-modal alignment and reasoning fine-tuning.                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **API Design**: Exposes research as an asynchronous background job, returning final structured reports with   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> detailed logs; supports tool integrations (web search, code interpreter, Model Context Protocol servers for     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> internal data research).                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Privacy and Safety by Design**: Restricts certain actions, logs all tool usage, requires trusted            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> connectors, and enforces separation of sensitive data workflows.                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Performance Benchmarks                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Academic and Research Benchmarks**:                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - State-of-the-art on Humanity’s Last Exam (26.6% - o3; new pass@1 of 41.6 for unified ChatGPT agent) and     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> GAIA (average accuracy of 72.6%).                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Well-suited for complex, multi-modal queries requiring synthesis across external and internal data.         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Analyst-Level Workflows**:                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Domain experts consistently rate Deep Research as automating multiple hours of challenging research per     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> successful query.                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Comparative Findings**:                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>   - Outperforms competitors (e.g., Gemini, DeepSeek, Perplexity) on multi-step analytic depth, citation         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> adherence, and complex chain-of-thought queries, albeit at higher cost and slower speeds.                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Integration Options                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **ChatGPT Interface**: Accessible through ChatGPT’s web and desktop/mobile apps by selecting “Deep Research”  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> in message composition.                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **API (Responses/Deep Research API)**: For developers, supports advanced integrations, automation, and tool   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> chaining, including the specification of custom data connectors and prompt rewriters.                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Connectors &amp; Third-Party Apps**: Proactive workflow automation via connectors to Gmail, GitHub, and         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> enterprise suites, with sandboxed and permissioned real-world actions via the ChatGPT Agent.                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Pricing &amp; Rate Limits**: Currently available to Pro, Plus, Team, and Enterprise users, with expansion to    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> the Free tier planned. The service is compute-intensive and billed at a premium (approx. $200/month), with      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> lower-latency variants and higher rate limits in development.                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Notable Strengths and Weaknesses                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> **Strengths**                                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Highest depth for source-rich, well-cited analytic research                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Iterative refinement and “human assistant” transparency in reasoning and decision logging                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Unified agentic system bridges complex research, web automation, and coding actions                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Robust safety (control over actions, explicit approvals, privacy isolation)                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Broad integration, especially for knowledge-intensive and enterprise research tasks                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> **Weaknesses**                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Processing time per research job (5–30 minutes) is substantially higher than general-purpose LLM outputs      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Cost is significantly higher than competitors for in-depth research                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Occasional hallucinations, confidence calibration issues, and variable output formatting                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - Research is most effective with fully-specified, well-formed prompts (API expects little to no clarification) <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> [1][2][3][5][6][7][8]                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ---                                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ## Comparative Analysis                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Core Capabilities and Use Case Specialization                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Gemini** excels as a general-purpose multimodal platform, rapidly handling diverse inputs (including        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> extremely long texts, images, video, and audio) with strong reasoning and large context support. Its agentic    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> variants (2.5 Deep Think) enable advanced planning and autonomy in complex workflows.                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Deep Research** is focused on deep, source-cited analytic synthesis—essentially automating the high-end     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> research analyst’s workflow with maximal coverage, transparency, and reasoning traceability. Its agentic        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> strengths shine in chaining multi-step queries, executing code, and integrating findings across disparate       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> domains.                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Architecture and Model Design                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Gemini** is built on sparse Mixture-of-Experts architecture for scalability and efficiency, and is natively <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> multimodal from the ground up. It is deeply embedded in Google’s infrastructure (TPUs, Workspace, Pixel).       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Deep Research** layers agentic reasoning atop advanced transformer backbones (optimized o3/o4), employs     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> reinforcement learning on tool-use tasks, and exposes fine-grained control and visibility via OpenAI’s API.     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Performance and Benchmarking                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Gemini** has benchmark leadership in large context, speed, and multimodal deployments (e.g., hours of       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> video, complex coding, factual QA), and generally edges out competitors for tasks blending modality and context <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> length.                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Deep Research** leads on traditional research metrics—multi-step logical synthesis, depth of citation, and  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> analyst-level report creation—as reflected in domain expert evaluations and results on reasoning-heavy          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> benchmarks (Humanity’s Last Exam, GAIA).                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Caveat on Benchmarks**: Both Google and OpenAI acknowledge the limitations of current benchmarks, with      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> issues around dataset validity, saturation, and safety signaling. Users should regard reported scores as        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> indicative but not absolute measures of real-world performance ([9]).                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Integration and Ecosystem                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Gemini** is highly accessible for developers and enterprises, offering mature APIs, Workspace integrations, <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> and ready deployment via Vertex AI with transparent pricing and extensive documentation. It supports            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> fine-tuning, customization, and flexible deployment models (cloud, on-device).                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Deep Research** is primarily available in ChatGPT and as an API for advanced developer use. Its workflow    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> capabilities are exposed via OpenAI's agent platform (ChatGPT Agent) and connectors, focusing on                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> knowledge-intensive enterprise environments and technical research.                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Cost, Access, and Scalability                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Gemini** offers broader cost tiers (including accessible entry points and enterprise pricing) and generous  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> free/developer access for basic use. Batch processing and context caching are available at reduced rates for    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> large-volume research.                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Deep Research** is positioned as a premium solution, with higher per-query costs and longer compute times.  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Rollout to broader user tiers and cost-optimized models is in progress.                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ### Strengths and Weaknesses Overview                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> |                  | Google Gemini                                         | OpenAI Deep Research               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> |                                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> |------------------|------------------------------------------------------|------------------------------------ <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ------|                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> | **Strengths**    | - Largest context, multimodal integration&lt;br&gt;- Speed, deep Google product integration&lt;br&gt;- <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Strong factual accuracy and enterprise scalability&lt;br&gt;- Comprehensive safety governance | - Unmatched depth in  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> citation-rich, analytic synthesis&lt;br&gt;- Transparent, step-wise reasoning &amp; action logs&lt;br&gt;- Agentic research     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> merges web/data/code tools&lt;br&gt;- Enterprise/analyst automation excellence     |                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> | **Weaknesses**   | - Occasional slowdowns at scale&lt;br&gt;- Over-refusal conservatism &lt;br&gt;- Some feature          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> gating&lt;br&gt;- Top-tier pricing for advanced models | - High cost, slow processing per job&lt;br&gt;-                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Hallucinations/confidence issues&lt;br&gt;- Prompts must be highly specific&lt;br&gt;- Format/UX issues in some outputs     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> |                                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ---                                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ## Conclusion                                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Google Gemini and OpenAI’s Deep Research agents, while both representing the cutting edge of AI, are tailored   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> to different segments of the knowledge-work and agent automation spectrum:                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Use Gemini** if you require fast, scalable, multimodal reasoning and content generation, integration with   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Google’s suite of tools, support for massive context windows, and broad developer deployment.                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> - **Use Deep Research** when you need the highest level of analytic synthesis, traceability, and source-backed  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> reporting—essential for research analysts, legal/technical experts, and enterprises that require auditable,     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> multi-step automation and actionability.                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Both platforms are converging towards more capable agentic AI, integrating multi-step reasoning, workflow       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> automation, and external tool use. User choice should be guided by the project’s required depth, speed,         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> modality, integration needs, and budget constraints.                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ---                                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ## Sources                                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 1. [Gemini models | Gemini API | Google AI for Developers](https://ai.google.dev/gemini-api/docs/models)        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 2. [Introducing Gemini: our largest and most capable AI                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> model](https://blog.google/technology/ai/google-gemini-ai/)                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 3. [Google models | Generative AI on Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/models)   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 4. [Gemini 2.5 Deep Think - Model Card -                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Googleapis.com](https://storage.googleapis.com/deepmind-media/Model-Cards/Gemini-2-5-Deep-Think-Model-Card.pdf) <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 5. [Introducing Gemini 2.0: our new AI model for the agentic                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> era](https://blog.google/technology/google-deepmind/google-gemini-ai-update-december-2024/)                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 6. [Gemini 2.5: Pushing the Frontier with Advanced Reasoning                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ...](https://storage.googleapis.com/deepmind-media/gemini/gemini_v2_5_report.pdf)                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 7. [Comparing Google Gemini and ChatGPT: API Keys, Costs &amp;                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Access](https://newo.ai/insights/google-gemini-vs-chatgpt-a-comprehensive-api-comparison/)                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 8. [ChatGP<PERSON> vs Claude vs Gemini: Full Report and Comparison of                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ...](https://www.datastudios.org/post/chatgpt-vs-claude-vs-gemini-full-report-and-comparison-of-features-perfor <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> mance-integrations-pric)                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 9. [ChatGP<PERSON> vs Claude vs Gemini: The Best AI Model for Each Use                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ...](https://creatoreconomy.so/p/chatgpt-vs-claude-vs-gemini-the-best-ai-model-for-each-use-case-2025)          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 10. [Rate limits | Gemini API | Google AI for Developers](https://ai.google.dev/gemini-api/docs/rate-limits)    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 11. [Gemini Developer API Pricing | Gemini API | Google AI for                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Developers](https://ai.google.dev/gemini-api/docs/pricing)                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 12. [Vertex AI Pricing | Generative AI on Vertex AI - Google                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Cloud](https://cloud.google.com/vertex-ai/generative-ai/pricing)                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 13. [Introducing deep research - OpenAI](https://openai.com/index/introducing-deep-research/)                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 14. [Introducing ChatGPT agent: bridging research and action -                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> OpenAI](https://openai.com/index/introducing-chatgpt-agent/)                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 15. [OpenAI's Deep Research Tool: A Comprehensive                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Overview](https://bytebridge.medium.com/openais-deep-research-tool-a-comprehensive-overview-12ddab43feff)       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 16. [Deep research - OpenAI API](https://platform.openai.com/docs/guides/deep-research)                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 17. [Introduction to deep research in the OpenAI                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> API](https://cookbook.openai.com/examples/deep_research_api/introduction_to_deep_research_api)                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 18. [OpenAI Deep Research vs. Google Deep Research - AI                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Supremacy](https://www.ai-supremacy.com/p/openai-deep-research-vs-google-deep)                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 19. [OpenAI Deep Research: How it Compares to Perplexity and                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Gemini](https://www.helicone.ai/blog/openai-deep-research)                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 20. [Can We Trust AI Benchmarks? An Interdisciplinary Review of ... -                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> arXiv](https://arxiv.org/html/2502.06559v2)                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Here is the final report: # Comprehensive Comparison: Google Gemini vs. OpenAI Deep Research Agents (2025)      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ## Introduction                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m This report provides a detailed, balanced comparison of Google’s Gemini AI suite and OpenAI’s Deep Research     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m agents. Both technologies represent state-of-the-art advancements in artificial intelligence but are designed   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m with distinct architectures, capabilities, use cases, and integration models. This analysis examines key        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m aspects such as core features, intended applications, underlying technologies, performance benchmarks,          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m integration options, and notable strengths and weaknesses, referencing the latest information and official      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m documentation as of August 2025.                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ---                                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ## Overview: Google Gemini                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Core Capabilities                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Multimodality**: Gemini supports native processing of text, images, video, audio, code, and PDF inputs, and \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m can generate outputs in text, audio, and structured data formats. Recent iterations, such as Gemini 2.5 Deep    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Think, can handle context windows exceeding 1 million tokens and output up to 192,000 tokens, enabling the      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m processing of vast and complex documents or media streams.                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Advanced Reasoning**: The “Deep Think” and Pro variants deploy parallel hypothesis testing and              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m reinforcement learning, excelling at coding, complex mathematical reasoning, multimodal understanding,          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m structured outputs, and function/tool calling.                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Agentic Capabilities**: Gemini powers research agents and universal AI assistants (e.g., Project Astra,     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Project Mariner), handling autonomous workflows like code debugging, real-time information retrieval, and       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m interactive web task completion.                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Safety and Governance**: Gemini employs extensive safety measures, including red teaming (automated and     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m external), multi-tiered abuse monitoring, mitigations against sensitive domains (e.g., chemical/biological),    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m and oversight by Google DeepMind’s Responsibility and Safety Council. It adheres to ethical principles such as  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m minimization of bias and toxicity.                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Intended Use Cases                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Complex knowledge work (e.g., scientific research, coding assistants, market analysis)                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Large-scale document analysis, summarization, and synthesis                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Content creation across text, image, video (e.g., AI-powered design, video generation with Veo, music with    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Lyria)                                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Autonomous agents for enterprise workflows (e.g., web research, spreadsheet automation, technical support,    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m creative design)                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Integration into Google products and services: Workspace (Docs, Sheets, Slides), Pixel devices, Search, Ads,  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Chrome, and Duet AI                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Underlying Technologies and Architecture                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Sparse Mixture-of-Experts (MoE) Transformers**: Efficiently routes token streams to specialist “experts,”   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m scaling model power and reducing unnecessary computation compared to dense models.                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Training Infrastructure**: Built and scaled atop Google’s custom Tensor Processing Units (TPUs), notably    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m the latest Trillium and v5p generations, offering high energy efficiency and throughput.                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Data and Multimodal Alignment**: Gemini models are instruction-tuned on diverse datasets spanning internet  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m text, code, multimodal data, and human tool-use demonstrations, with additional reinforcement from human and AI \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m feedback loops.                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Agentic Systems**: Gemini integrates tool use (code execution, search), rapid context expansion (handling   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m very large documents), and interaction with third-party tools through its APIs.                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Performance Benchmarks                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Academic and Coding Tasks**:                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Outperformed humans on MMLU (90.0% - Gemini Ultra) and competes at IMO (mathematics competition) Bronze     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m level (60.7%).                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Achieves strong results on coding (LiveCodeBench 87.6%), knowledge, and reasoning benchmarks.               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Agentic Tasks**:                                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Project Mariner and Gemini Plays Pokémon showcase advanced planning, tool use, and context management,      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m e.g., completing Pokémon Blue autonomously after hundreds of hours.                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Comparative Edge**:                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Gemini is recognized for high factual accuracy, extremely large context windows, and rapid processing,      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m especially for multimodal or large-scale tasks.                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Integration Options                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **APIs**: Accessible via Google AI Studio, Vertex AI (for cloud deployment), and dedicated APIs (REST, SDKs   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m in various languages).                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Product Deep Integration**: Gemini is foundational to Workspace tools, Pixel on-device AI, Search Labs' AI  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Overviews, and Duet AI for enterprise productivity.                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Developer Ecosystem**: Offers Model Garden for testing/deployment, detailed rate limits with scalable       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m quotas, batch mode support for cost efficiency, and transparent, usage-based pricing.                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Fine-tuning & Customization**: Support for model customization and domain-specific tasks, with facilities   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m for prompt engineering, tool chaining, and safe deployment monitoring.                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Notable Strengths and Weaknesses                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m **Strengths**                                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Broad and deep multimodality with vast context (1M+ tokens)                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - High-speed inference for large, complex workflows                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Deep integration across Google’s ecosystem and developer tools                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Industry-leading benchmarking on factual, reasoning, and coding tasks                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Advanced safety governance                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m **Weaknesses**                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Some known issues with hallucination and occasional latency, particularly at the highest computation tiers    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Over-refusal of benign queries due to conservative safety layers                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Earlier access to specialized features (e.g., newest model variants) may be gated or subject to usage         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m restrictions                                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Premium enterprise pricing for advanced models and batch processing                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m [1][2][3][4][5][6][7][8][9][10][11][12]                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ---                                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ## Overview: OpenAI Deep Research Agents                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Core Capabilities                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Autonomous Multi-Step Research**: Deep Research agents can independently plan, browse the real-time         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m internet, analyze hundreds of resources (websites, PDFs, images, structured files), synthesize findings, and    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m generate comprehensive, citation-backed research reports. Transparency is emphasized through step-by-step       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m action logs and in-line source attribution.                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Agentic Reasoning & Workflow Automation**: Empowers users to delegate entire research projects, from web    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m data collection to report writing, planning, and visualization. The newly launched ChatGPT Agent combines these \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m capabilities with code execution, web automation, and third-party tool integration (e.g., creating              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m presentations or spreadsheets).                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Citations & Analytical Traceability**: For every decisive step, Deep Research logs tools invoked (web       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m search, code interpreters, Model Context Protocol connectors), building a traceable reasoning chain akin to an  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m expert analyst’s workflow.                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Safety & User Control**: Implements explicit user approval for consequential actions, privacy/sandboxing    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m for sensitive tasks, and comprehensive risk mitigations, including prompt-injection and data exfiltration       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m defenses.                                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Intended Use Cases                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - In-depth market, policy, technical, or literature research requiring source-backed synthesis                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Competitive intelligence, product comparison, and strategy reports                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Automated health economics analysis, regulatory compliance checks, and legal research                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Spreadsheet automation, technical reporting, real-time trend and news aggregation                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Enterprise integrations (Gmail, GitHub, financial modeling) and advanced agentic workflows                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Underlying Technologies and Architecture                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Large-scale Reasoning Models**: Deep Research leverages optimized variants of OpenAI’s o3 and forthcoming   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m o4 models, which are highly agentic, employing transformer backbones enhanced by real-world tool-use learning   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m and advanced browsing capabilities.                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Reinforcement Learning on Real Tasks**: Trained through reinforcement learning on real                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m browser/Python/coding tasks, with extensive multi-modal alignment and reasoning fine-tuning.                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **API Design**: Exposes research as an asynchronous background job, returning final structured reports with   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m detailed logs; supports tool integrations (web search, code interpreter, Model Context Protocol servers for     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m internal data research).                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Privacy and Safety by Design**: Restricts certain actions, logs all tool usage, requires trusted            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m connectors, and enforces separation of sensitive data workflows.                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Performance Benchmarks                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Academic and Research Benchmarks**:                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - State-of-the-art on Humanity’s Last Exam (26.6% - o3; new pass@1 of 41.6 for unified ChatGPT agent) and     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m GAIA (average accuracy of 72.6%).                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Well-suited for complex, multi-modal queries requiring synthesis across external and internal data.         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Analyst-Level Workflows**:                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Domain experts consistently rate Deep Research as automating multiple hours of challenging research per     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m successful query.                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Comparative Findings**:                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m   - Outperforms competitors (e.g., Gemini, DeepSeek, Perplexity) on multi-step analytic depth, citation         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m adherence, and complex chain-of-thought queries, albeit at higher cost and slower speeds.                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Integration Options                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **ChatGPT Interface**: Accessible through ChatGPT’s web and desktop/mobile apps by selecting “Deep Research”  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m in message composition.                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **API (Responses/Deep Research API)**: For developers, supports advanced integrations, automation, and tool   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m chaining, including the specification of custom data connectors and prompt rewriters.                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Connectors & Third-Party Apps**: Proactive workflow automation via connectors to Gmail, GitHub, and         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m enterprise suites, with sandboxed and permissioned real-world actions via the ChatGPT Agent.                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Pricing & Rate Limits**: Currently available to Pro, Plus, Team, and Enterprise users, with expansion to    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m the Free tier planned. The service is compute-intensive and billed at a premium (approx. $200/month), with      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m lower-latency variants and higher rate limits in development.                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Notable Strengths and Weaknesses                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m **Strengths**                                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Highest depth for source-rich, well-cited analytic research                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Iterative refinement and “human assistant” transparency in reasoning and decision logging                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Unified agentic system bridges complex research, web automation, and coding actions                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Robust safety (control over actions, explicit approvals, privacy isolation)                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Broad integration, especially for knowledge-intensive and enterprise research tasks                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m **Weaknesses**                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Processing time per research job (5–30 minutes) is substantially higher than general-purpose LLM outputs      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Cost is significantly higher than competitors for in-depth research                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Occasional hallucinations, confidence calibration issues, and variable output formatting                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - Research is most effective with fully-specified, well-formed prompts (API expects little to no clarification) \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m [1][2][3][5][6][7][8]                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ---                                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ## Comparative Analysis                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Core Capabilities and Use Case Specialization                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Gemini** excels as a general-purpose multimodal platform, rapidly handling diverse inputs (including        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m extremely long texts, images, video, and audio) with strong reasoning and large context support. Its agentic    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m variants (2.5 Deep Think) enable advanced planning and autonomy in complex workflows.                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Deep Research** is focused on deep, source-cited analytic synthesis—essentially automating the high-end     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m research analyst’s workflow with maximal coverage, transparency, and reasoning traceability. Its agentic        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m strengths shine in chaining multi-step queries, executing code, and integrating findings across disparate       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m domains.                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Architecture and Model Design                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Gemini** is built on sparse Mixture-of-Experts architecture for scalability and efficiency, and is natively \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m multimodal from the ground up. It is deeply embedded in Google’s infrastructure (TPUs, Workspace, Pixel).       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Deep Research** layers agentic reasoning atop advanced transformer backbones (optimized o3/o4), employs     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m reinforcement learning on tool-use tasks, and exposes fine-grained control and visibility via OpenAI’s API.     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Performance and Benchmarking                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **<PERSON>** has benchmark leadership in large context, speed, and multimodal deployments (e.g., hours of       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m video, complex coding, factual QA), and generally edges out competitors for tasks blending modality and context \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m length.                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Deep Research** leads on traditional research metrics—multi-step logical synthesis, depth of citation, and  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m analyst-level report creation—as reflected in domain expert evaluations and results on reasoning-heavy          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m benchmarks (Humanity’s Last Exam, GAIA).                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Caveat on Benchmarks**: Both Google and OpenAI acknowledge the limitations of current benchmarks, with      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m issues around dataset validity, saturation, and safety signaling. Users should regard reported scores as        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m indicative but not absolute measures of real-world performance ([9]).                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Integration and Ecosystem                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Gemini** is highly accessible for developers and enterprises, offering mature APIs, Workspace integrations, \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m and ready deployment via Vertex AI with transparent pricing and extensive documentation. It supports            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m fine-tuning, customization, and flexible deployment models (cloud, on-device).                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Deep Research** is primarily available in ChatGPT and as an API for advanced developer use. Its workflow    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m capabilities are exposed via OpenAI's agent platform (ChatGPT Agent) and connectors, focusing on                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m knowledge-intensive enterprise environments and technical research.                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Cost, Access, and Scalability                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Gemini** offers broader cost tiers (including accessible entry points and enterprise pricing) and generous  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m free/developer access for basic use. Batch processing and context caching are available at reduced rates for    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m large-volume research.                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Deep Research** is positioned as a premium solution, with higher per-query costs and longer compute times.  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Rollout to broader user tiers and cost-optimized models is in progress.                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ### Strengths and Weaknesses Overview                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m |                  | Google Gemini                                         | OpenAI Deep Research               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m |                                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m |------------------|------------------------------------------------------|------------------------------------ \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ------|                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m | **Strengths**    | - Largest context, multimodal integration<br>- Speed, deep Google product integration<br>- \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Strong factual accuracy and enterprise scalability<br>- Comprehensive safety governance | - Unmatched depth in  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m citation-rich, analytic synthesis<br>- Transparent, step-wise reasoning & action logs<br>- Agentic research     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m merges web/data/code tools<br>- Enterprise/analyst automation excellence     |                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m | **Weaknesses**   | - Occasional slowdowns at scale<br>- Over-refusal conservatism <br>- Some feature          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m gating<br>- Top-tier pricing for advanced models | - High cost, slow processing per job<br>-                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Hallucinations/confidence issues<br>- Prompts must be highly specific<br>- Format/UX issues in some outputs     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m |                                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ---                                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ## Conclusion                                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Google Gemini and OpenAI’s Deep Research agents, while both representing the cutting edge of AI, are tailored   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m to different segments of the knowledge-work and agent automation spectrum:                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Use Gemini** if you require fast, scalable, multimodal reasoning and content generation, integration with   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Google’s suite of tools, support for massive context windows, and broad developer deployment.                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m - **Use Deep Research** when you need the highest level of analytic synthesis, traceability, and source-backed  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m reporting—essential for research analysts, legal/technical experts, and enterprises that require auditable,     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m multi-step automation and actionability.                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Both platforms are converging towards more capable agentic AI, integrating multi-step reasoning, workflow       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m automation, and external tool use. User choice should be guided by the project’s required depth, speed,         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m modality, integration needs, and budget constraints.                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ---                                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ## Sources                                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 1. [Gemini models | Gemini API | Google AI for Developers](https://ai.google.dev/gemini-api/docs/models)        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 2. [Introducing Gemini: our largest and most capable AI                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m model](https://blog.google/technology/ai/google-gemini-ai/)                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 3. [Google models | Generative AI on Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/models)   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 4. [Gemini 2.5 Deep Think - Model Card -                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Googleapis.com](https://storage.googleapis.com/deepmind-media/Model-Cards/Gemini-2-5-Deep-Think-Model-Card.pdf) \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 5. [Introducing Gemini 2.0: our new AI model for the agentic                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m era](https://blog.google/technology/google-deepmind/google-gemini-ai-update-december-2024/)                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 6. [Gemini 2.5: Pushing the Frontier with Advanced Reasoning                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ...](https://storage.googleapis.com/deepmind-media/gemini/gemini_v2_5_report.pdf)                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 7. [Comparing Google Gemini and ChatGPT: API Keys, Costs &                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Access](https://newo.ai/insights/google-gemini-vs-chatgpt-a-comprehensive-api-comparison/)                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 8. [<PERSON>tGP<PERSON> vs Claude vs Gemini: Full Report and Comparison of                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ...](https://www.datastudios.org/post/chatgpt-vs-claude-vs-gemini-full-report-and-comparison-of-features-perfor \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m mance-integrations-pric)                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 9. [<PERSON>tGPT vs Claude vs Gemini: The Best AI Model for Each Use                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ...](https://creatoreconomy.so/p/chatgpt-vs-claude-vs-gemini-the-best-ai-model-for-each-use-case-2025)          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 10. [Rate limits | Gemini API | Google AI for Developers](https://ai.google.dev/gemini-api/docs/rate-limits)    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 11. [Gemini Developer API Pricing | Gemini API | Google AI for                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Developers](https://ai.google.dev/gemini-api/docs/pricing)                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 12. [Vertex AI Pricing | Generative AI on Vertex AI - Google                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Cloud](https://cloud.google.com/vertex-ai/generative-ai/pricing)                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 13. [Introducing deep research - OpenAI](https://openai.com/index/introducing-deep-research/)                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 14. [Introducing ChatGPT agent: bridging research and action -                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m OpenAI](https://openai.com/index/introducing-chatgpt-agent/)                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 15. [OpenAI's Deep Research Tool: A Comprehensive                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Overview](https://bytebridge.medium.com/openais-deep-research-tool-a-comprehensive-overview-12ddab43feff)       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 16. [Deep research - OpenAI API](https://platform.openai.com/docs/guides/deep-research)                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 17. [Introduction to deep research in the OpenAI                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m API](https://cookbook.openai.com/examples/deep_research_api/introduction_to_deep_research_api)                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 18. [OpenAI Deep Research vs. Google Deep Research - AI                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Supremacy](https://www.ai-supremacy.com/p/openai-deep-research-vs-google-deep)                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 19. [OpenAI Deep Research: How it Compares to Perplexity and                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m <PERSON>](https://www.helicone.ai/blog/openai-deep-research)                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m 20. [Can We Trust AI Benchmarks? An Interdisciplinary Review of ... -                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m arXiv](https://arxiv.org/html/2502.06559v2)                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["format_messages(result['messages'])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                 <span style=\"font-weight: bold\">Comprehensive Comparison: Google Gemini vs. OpenAI Deep Research Agents (2025)</span>                  ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "\n", "                                                   <span style=\"font-weight: bold; text-decoration: underline\">Introduction</span>                                                    \n", "\n", "This report provides a detailed, balanced comparison of Google’s Gemini AI suite and OpenAI’s Deep Research agents.\n", "Both technologies represent state-of-the-art advancements in artificial intelligence but are designed with distinct\n", "architectures, capabilities, use cases, and integration models. This analysis examines key aspects such as core    \n", "features, intended applications, underlying technologies, performance benchmarks, integration options, and notable \n", "strengths and weaknesses, referencing the latest information and official documentation as of August 2025.         \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000\">───────────────────────────────────────────────────────────────────────────────────────────────────────────────────</span>\n", "\n", "                                              <span style=\"font-weight: bold; text-decoration: underline\">Overview: Google Gemini</span>                                              \n", "\n", "                                                 <span style=\"font-weight: bold\">Core Capabilities</span>                                                 \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Multimodality</span>: Gemini supports native processing of text, images, video, audio, code, and PDF inputs, and can   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>generate outputs in text, audio, and structured data formats. Recent iterations, such as Gemini 2.5 Deep Think, \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>can handle context windows exceeding 1 million tokens and output up to 192,000 tokens, enabling the processing  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>of vast and complex documents or media streams.                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Advanced Reasoning</span>: The “Deep Think” and Pro variants deploy parallel hypothesis testing and reinforcement      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>learning, excelling at coding, complex mathematical reasoning, multimodal understanding, structured outputs, and\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>function/tool calling.                                                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Agentic Capabilities</span>: Gemini powers research agents and universal AI assistants (e.g., Project Astra, Project   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>Mariner), handling autonomous workflows like code debugging, real-time information retrieval, and interactive   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>web task completion.                                                                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Safety and Governance</span>: Gemini employs extensive safety measures, including red teaming (automated and external),\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>multi-tiered abuse monitoring, mitigations against sensitive domains (e.g., chemical/biological), and oversight \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>by Google DeepMind’s Responsibility and Safety Council. It adheres to ethical principles such as minimization of\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>bias and toxicity.                                                                                              \n", "\n", "                                                <span style=\"font-weight: bold\">Intended Use Cases</span>                                                 \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Complex knowledge work (e.g., scientific research, coding assistants, market analysis)                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Large-scale document analysis, summarization, and synthesis                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Content creation across text, image, video (e.g., AI-powered design, video generation with Veo, music with      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>Lyria)                                                                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Autonomous agents for enterprise workflows (e.g., web research, spreadsheet automation, technical support,      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>creative design)                                                                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Integration into Google products and services: Workspace (Docs, Sheets, Slides), Pixel devices, Search, Ads,    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>Chrome, and Duet AI                                                                                             \n", "\n", "                                     <span style=\"font-weight: bold\">Underlying Technologies and Architecture</span>                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Sparse Mixture-of-Experts (MoE) Transformers</span>: Efficiently routes token streams to specialist “experts,” scaling \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>model power and reducing unnecessary computation compared to dense models.                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Training Infrastructure</span>: Built and scaled atop Google’s custom Tensor Processing Units (TPUs), notably the      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>latest Trillium and v5p generations, offering high energy efficiency and throughput.                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Data and Multimodal Alignment</span>: Gemini models are instruction-tuned on diverse datasets spanning internet text,  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>code, multimodal data, and human tool-use demonstrations, with additional reinforcement from human and AI       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>feedback loops.                                                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Agentic Systems</span>: Gemini integrates tool use (code execution, search), rapid context expansion (handling very    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>large documents), and interaction with third-party tools through its APIs.                                      \n", "\n", "                                              <span style=\"font-weight: bold\">Performance Benchmarks</span>                                               \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Academic and Coding Tasks</span>:                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Outperformed humans on MMLU (90.0% - Gemini Ultra) and competes at IMO (mathematics competition) Bronze level\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>(60.7%).                                                                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Achieves strong results on coding (LiveCodeBench 87.6%), knowledge, and reasoning benchmarks.                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Agentic Tasks</span>:                                                                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Project Mariner and Gemini Plays Pokémon showcase advanced planning, tool use, and context management, e.g., \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>completing Pokémon Blue autonomously after hundreds of hours.                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Comparative Edge</span>:                                                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Gemini is recognized for high factual accuracy, extremely large context windows, and rapid processing,       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>especially for multimodal or large-scale tasks.                                                              \n", "\n", "                                                <span style=\"font-weight: bold\">Integration Options</span>                                                \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">APIs</span>: Accessible via Google AI Studio, Vertex AI (for cloud deployment), and dedicated APIs (REST, SDKs in      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>various languages).                                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Product Deep Integration</span>: Gemini is foundational to Workspace tools, Pixel on-device AI, Search Labs' AI        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>Overviews, and Duet AI for enterprise productivity.                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Developer Ecosystem</span>: Offers Model Garden for testing/deployment, detailed rate limits with scalable quotas,     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>batch mode support for cost efficiency, and transparent, usage-based pricing.                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Fine-tuning &amp; Customization</span>: Support for model customization and domain-specific tasks, with facilities for     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>prompt engineering, tool chaining, and safe deployment monitoring.                                              \n", "\n", "                                         <span style=\"font-weight: bold\">Notable Strengths and Weaknesses</span>                                          \n", "\n", "<span style=\"font-weight: bold\">Strengths</span>                                                                                                          \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Broad and deep multimodality with vast context (1M+ tokens)                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>High-speed inference for large, complex workflows                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Deep integration across Google’s ecosystem and developer tools                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Industry-leading benchmarking on factual, reasoning, and coding tasks                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Advanced safety governance                                                                                      \n", "\n", "<span style=\"font-weight: bold\">Weaknesses</span>                                                                                                         \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Some known issues with hallucination and occasional latency, particularly at the highest computation tiers      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Over-refusal of benign queries due to conservative safety layers                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Earlier access to specialized features (e.g., newest model variants) may be gated or subject to usage           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>restrictions                                                                                                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Premium enterprise pricing for advanced models and batch processing                                             \n", "\n", "[1][2][3][4][5][6][7][8][9][10][11][12]                                                                            \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000\">───────────────────────────────────────────────────────────────────────────────────────────────────────────────────</span>\n", "\n", "                                       <span style=\"font-weight: bold; text-decoration: underline\">Overview: OpenAI Deep Research Agents</span>                                       \n", "\n", "                                                 <span style=\"font-weight: bold\">Core Capabilities</span>                                                 \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Autonomous Multi-Step Research</span>: Deep Research agents can independently plan, browse the real-time internet,     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>analyze hundreds of resources (websites, PDFs, images, structured files), synthesize findings, and generate     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>comprehensive, citation-backed research reports. Transparency is emphasized through step-by-step action logs and\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>in-line source attribution.                                                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Agentic Reasoning &amp; Workflow Automation</span>: Empowers users to delegate entire research projects, from web data     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>collection to report writing, planning, and visualization. The newly launched ChatGPT Agent combines these      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>capabilities with code execution, web automation, and third-party tool integration (e.g., creating presentations\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>or spreadsheets).                                                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Citations &amp; Analytical Traceability</span>: For every decisive step, Deep Research logs tools invoked (web search, code\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>interpreters, Model Context Protocol connectors), building a traceable reasoning chain akin to an expert        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>analyst’s workflow.                                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Safety &amp; User Control</span>: Implements explicit user approval for consequential actions, privacy/sandboxing for      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>sensitive tasks, and comprehensive risk mitigations, including prompt-injection and data exfiltration defenses. \n", "\n", "                                                <span style=\"font-weight: bold\">Intended Use Cases</span>                                                 \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>In-depth market, policy, technical, or literature research requiring source-backed synthesis                    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Competitive intelligence, product comparison, and strategy reports                                              \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Automated health economics analysis, regulatory compliance checks, and legal research                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Spreadsheet automation, technical reporting, real-time trend and news aggregation                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Enterprise integrations (Gmail, GitHub, financial modeling) and advanced agentic workflows                      \n", "\n", "                                     <span style=\"font-weight: bold\">Underlying Technologies and Architecture</span>                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Large-scale Reasoning Models</span>: Deep Research leverages optimized variants of OpenAI’s o3 and forthcoming o4      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>models, which are highly agentic, employing transformer backbones enhanced by real-world tool-use learning and  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>advanced browsing capabilities.                                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Reinforcement Learning on Real Tasks</span>: Trained through reinforcement learning on real browser/Python/coding      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>tasks, with extensive multi-modal alignment and reasoning fine-tuning.                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">API Design</span>: Exposes research as an asynchronous background job, returning final structured reports with detailed\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>logs; supports tool integrations (web search, code interpreter, Model Context Protocol servers for internal data\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>research).                                                                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Privacy and Safety by Design</span>: Restricts certain actions, logs all tool usage, requires trusted connectors, and  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>enforces separation of sensitive data workflows.                                                                \n", "\n", "                                              <span style=\"font-weight: bold\">Performance Benchmarks</span>                                               \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Academic and Research Benchmarks</span>:                                                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>State-of-the-art on Humanity’s Last Exam (26.6% - o3; new pass@1 of 41.6 for unified ChatGPT agent) and GAIA \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>(average accuracy of 72.6%).                                                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Well-suited for complex, multi-modal queries requiring synthesis across external and internal data.          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Analyst-Level Workflows</span>:                                                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Domain experts consistently rate Deep Research as automating multiple hours of challenging research per      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>successful query.                                                                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Comparative Findings</span>:                                                                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">    • </span>Outperforms competitors (e.g., Gemini, DeepSeek, Perplexity) on multi-step analytic depth, citation          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">      </span>adherence, and complex chain-of-thought queries, albeit at higher cost and slower speeds.                    \n", "\n", "                                                <span style=\"font-weight: bold\">Integration Options</span>                                                \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">ChatGPT Interface</span>: Accessible through ChatGPT’s web and desktop/mobile apps by selecting “Deep Research” in     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>message composition.                                                                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">API (Responses/Deep Research API)</span>: For developers, supports advanced integrations, automation, and tool         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>chaining, including the specification of custom data connectors and prompt rewriters.                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Connectors &amp; Third-Party Apps</span>: Proactive workflow automation via connectors to Gmail, GitHub, and enterprise    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>suites, with sandboxed and permissioned real-world actions via the ChatGPT Agent.                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Pricing &amp; Rate Limits</span>: Currently available to Pro, Plus, Team, and Enterprise users, with expansion to the Free \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>tier planned. The service is compute-intensive and billed at a premium (approx. $200/month), with lower-latency \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>variants and higher rate limits in development.                                                                 \n", "\n", "                                         <span style=\"font-weight: bold\">Notable Strengths and Weaknesses</span>                                          \n", "\n", "<span style=\"font-weight: bold\">Strengths</span>                                                                                                          \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Highest depth for source-rich, well-cited analytic research                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Iterative refinement and “human assistant” transparency in reasoning and decision logging                       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Unified agentic system bridges complex research, web automation, and coding actions                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Robust safety (control over actions, explicit approvals, privacy isolation)                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Broad integration, especially for knowledge-intensive and enterprise research tasks                             \n", "\n", "<span style=\"font-weight: bold\">Weaknesses</span>                                                                                                         \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Processing time per research job (5–30 minutes) is substantially higher than general-purpose LLM outputs        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Cost is significantly higher than competitors for in-depth research                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Occasional hallucinations, confidence calibration issues, and variable output formatting                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Research is most effective with fully-specified, well-formed prompts (API expects little to no clarification)   \n", "\n", "[1][2][3][5][6][7][8]                                                                                              \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000\">───────────────────────────────────────────────────────────────────────────────────────────────────────────────────</span>\n", "\n", "                                               <span style=\"font-weight: bold; text-decoration: underline\">Comparative Analysis</span>                                                \n", "\n", "                                   <span style=\"font-weight: bold\">Core Capabilities and Use Case Specialization</span>                                   \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Gemini</span> excels as a general-purpose multimodal platform, rapidly handling diverse inputs (including extremely    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>long texts, images, video, and audio) with strong reasoning and large context support. Its agentic variants (2.5\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>Deep Think) enable advanced planning and autonomy in complex workflows.                                         \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Deep Research</span> is focused on deep, source-cited analytic synthesis—essentially automating the high-end research  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>analyst’s workflow with maximal coverage, transparency, and reasoning traceability. Its agentic strengths shine \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>in chaining multi-step queries, executing code, and integrating findings across disparate domains.              \n", "\n", "                                           <span style=\"font-weight: bold\">Architecture and Model Design</span>                                           \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Gemini</span> is built on sparse Mixture-of-Experts architecture for scalability and efficiency, and is natively       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>multimodal from the ground up. It is deeply embedded in Google’s infrastructure (TPUs, Workspace, Pixel).       \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Deep Research</span> layers agentic reasoning atop advanced transformer backbones (optimized o3/o4), employs           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>reinforcement learning on tool-use tasks, and exposes fine-grained control and visibility via OpenAI’s API.     \n", "\n", "                                           <span style=\"font-weight: bold\">Performance and Benchmarking</span>                                            \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Gemini</span> has benchmark leadership in large context, speed, and multimodal deployments (e.g., hours of video,      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>complex coding, factual QA), and generally edges out competitors for tasks blending modality and context length.\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Deep Research</span> leads on traditional research metrics—multi-step logical synthesis, depth of citation, and        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>analyst-level report creation—as reflected in domain expert evaluations and results on reasoning-heavy          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>benchmarks (Humanity’s Last Exam, GAIA).                                                                        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Caveat on Benchmarks</span>: Both Google and OpenAI acknowledge the limitations of current benchmarks, with issues     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>around dataset validity, saturation, and safety signaling. Users should regard reported scores as indicative but\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>not absolute measures of real-world performance ([9]).                                                          \n", "\n", "                                             <span style=\"font-weight: bold\">Integration and Ecosystem</span>                                             \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Gemini</span> is highly accessible for developers and enterprises, offering mature APIs, Workspace integrations, and   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>ready deployment via Vertex AI with transparent pricing and extensive documentation. It supports fine-tuning,   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>customization, and flexible deployment models (cloud, on-device).                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Deep Research</span> is primarily available in ChatGPT and as an API for advanced developer use. Its workflow          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>capabilities are exposed via OpenAI's agent platform (ChatGPT Agent) and connectors, focusing on                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>knowledge-intensive enterprise environments and technical research.                                             \n", "\n", "                                           <span style=\"font-weight: bold\">Cost, Access, and Scalability</span>                                           \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Gemini</span> offers broader cost tiers (including accessible entry points and enterprise pricing) and generous        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>free/developer access for basic use. Batch processing and context caching are available at reduced rates for    \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>large-volume research.                                                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Deep Research</span> is positioned as a premium solution, with higher per-query costs and longer compute times. Rollout\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>to broader user tiers and cost-optimized models is in progress.                                                 \n", "\n", "                                         <span style=\"font-weight: bold\">Strengths and Weaknesses Overview</span>                                         \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "                                                                                                                   \n", " <span style=\"font-weight: bold\">            </span> <span style=\"font-weight: bold\"> Google Gemini                                   </span> <span style=\"font-weight: bold\"> OpenAI Deep Research                             </span> \n", " ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ \n", "  <span style=\"font-weight: bold\">Strengths</span>    - Largest context, multimodal integration-        - Unmatched depth in citation-rich, analytic      \n", "               Speed, deep Google product integration- Strong    synthesis- Transparent, step-wise reasoning &amp;     \n", "               factual accuracy and enterprise scalability-      action logs- Agentic research merges              \n", "               Comprehensive safety governance                   web/data/code tools- Enterprise/analyst           \n", "                                                                 automation excellence                             \n", "  <span style=\"font-weight: bold\">Weaknesses</span>   - Occasional slowdowns at scale- Over-refusal     - High cost, slow processing per job-             \n", "               conservatism - Some feature gating- Top-tier      Hallucinations/confidence issues- Prompts must    \n", "               pricing for advanced models                       be highly specific- Format/UX issues in some      \n", "                                                                 outputs                                           \n", "                                                                                                                   \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000\">───────────────────────────────────────────────────────────────────────────────────────────────────────────────────</span>\n", "\n", "                                                    <span style=\"font-weight: bold; text-decoration: underline\">Conclusion</span>                                                     \n", "\n", "Google Gemini and OpenAI’s Deep Research agents, while both representing the cutting edge of AI, are tailored to   \n", "different segments of the knowledge-work and agent automation spectrum:                                            \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Use Gemini</span> if you require fast, scalable, multimodal reasoning and content generation, integration with Google’s\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>suite of tools, support for massive context windows, and broad developer deployment.                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span><span style=\"font-weight: bold\">Use Deep Research</span> when you need the highest level of analytic synthesis, traceability, and source-backed        \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>reporting—essential for research analysts, legal/technical experts, and enterprises that require auditable,     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>multi-step automation and actionability.                                                                        \n", "\n", "Both platforms are converging towards more capable agentic AI, integrating multi-step reasoning, workflow          \n", "automation, and external tool use. User choice should be guided by the project’s required depth, speed, modality,  \n", "integration needs, and budget constraints.                                                                         \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000\">───────────────────────────────────────────────────────────────────────────────────────────────────────────────────</span>\n", "\n", "                                                      <span style=\"font-weight: bold; text-decoration: underline\">Sources</span>                                                      \n", "\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  1 </span><a href=\"https://ai.google.dev/gemini-api/docs/models\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Gemini models | Gemini API | Google AI for Developers</span></a>                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  2 </span><a href=\"https://blog.google/technology/ai/google-gemini-ai/\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Introducing Gemini: our largest and most capable AI model</span></a>                                                      \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  3 </span><a href=\"https://cloud.google.com/vertex-ai/generative-ai/docs/models\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Google models | Generative AI on Vertex AI</span></a>                                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  4 </span><a href=\"https://storage.googleapis.com/deepmind-media/Model-Cards/Gemini-2-5-Deep-Think-Model-Card.pdf\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Gemini 2.5 Deep Think - Model Card - Googleapis.com</span></a>                                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  5 </span><a href=\"https://blog.google/technology/google-deepmind/google-gemini-ai-update-december-2024/\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Introducing Gemini 2.0: our new AI model for the agentic era</span></a>                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  6 </span><a href=\"https://storage.googleapis.com/deepmind-media/gemini/gemini_v2_5_report.pdf\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Gemini 2.5: Pushing the Frontier with Advanced Reasoning ...</span></a>                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  7 </span><a href=\"https://newo.ai/insights/google-gemini-vs-chatgpt-a-comprehensive-api-comparison/\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Comparing Google Gemini and ChatGPT: API Keys, Costs &amp; Access</span></a>                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  8 </span><a href=\"https://www.datastudios.org/post/chatgpt-vs-claude-vs-gemini-full-report-and-comparison-of-features-performance-integrations-pric\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">ChatGPT vs Claude vs Gemini: Full Report and Comparison of ...</span></a>                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">  9 </span><a href=\"https://creatoreconomy.so/p/chatgpt-vs-claude-vs-gemini-the-best-ai-model-for-each-use-case-2025\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">ChatGPT vs Claude vs Gemini: The Best AI Model for Each Use ...</span></a>                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 10 </span><a href=\"https://ai.google.dev/gemini-api/docs/rate-limits\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Rate limits | Gemini API | Google AI for Developers</span></a>                                                            \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 11 </span><a href=\"https://ai.google.dev/gemini-api/docs/pricing\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Gemini Developer API Pricing | Gemini API | Google AI for Developers</span></a>                                           \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 12 </span><a href=\"https://cloud.google.com/vertex-ai/generative-ai/pricing\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Vertex AI Pricing | Generative AI on Vertex AI - Google Cloud</span></a>                                                  \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 13 </span><a href=\"https://openai.com/index/introducing-deep-research/\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Introducing deep research - OpenAI</span></a>                                                                             \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 14 </span><a href=\"https://openai.com/index/introducing-chatgpt-agent/\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Introducing ChatGPT agent: bridging research and action - OpenAI</span></a>                                               \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 15 </span><a href=\"https://bytebridge.medium.com/openais-deep-research-tool-a-comprehensive-overview-12ddab43feff\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">OpenAI's Deep Research Tool: A Comprehensive Overview</span></a>                                                          \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 16 </span><a href=\"https://platform.openai.com/docs/guides/deep-research\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Deep research - OpenAI API</span></a>                                                                                     \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 17 </span><a href=\"https://cookbook.openai.com/examples/deep_research_api/introduction_to_deep_research_api\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Introduction to deep research in the OpenAI API</span></a>                                                                \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 18 </span><a href=\"https://www.ai-supremacy.com/p/openai-deep-research-vs-google-deep\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">OpenAI Deep Research vs. Google Deep Research - AI Supremacy</span></a>                                                   \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 19 </span><a href=\"https://www.helicone.ai/blog/openai-deep-research\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">OpenAI Deep Research: How it Compares to Perplexity and Gemini</span></a>                                                 \n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 20 </span><a href=\"https://arxiv.org/html/2502.06559v2\" target=\"_blank\"><span style=\"color: #000080; text-decoration-color: #000080; text-decoration: underline\">Can We Trust AI Benchmarks? An Interdisciplinary Review of ... - arXiv</span></a>                                         \n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃                 \u001b[1mComprehensive Comparison: Google Gemini vs. OpenAI Deep Research Agents (2025)\u001b[0m                  ┃\n", "┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\n", "\n", "\n", "                                                   \u001b[1;4mIntroduction\u001b[0m                                                    \n", "\n", "This report provides a detailed, balanced comparison of Google’s Gemini AI suite and OpenAI’s Deep Research agents.\n", "Both technologies represent state-of-the-art advancements in artificial intelligence but are designed with distinct\n", "architectures, capabilities, use cases, and integration models. This analysis examines key aspects such as core    \n", "features, intended applications, underlying technologies, performance benchmarks, integration options, and notable \n", "strengths and weaknesses, referencing the latest information and official documentation as of August 2025.         \n", "\n", "\u001b[33m───────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m\n", "\n", "                                              \u001b[1;4mOverview: Google Gemini\u001b[0m                                              \n", "\n", "                                                 \u001b[1mCore Capabilities\u001b[0m                                                 \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mMultimodality\u001b[0m: Gemini supports native processing of text, images, video, audio, code, and PDF inputs, and can   \n", "\u001b[1;33m   \u001b[0mgenerate outputs in text, audio, and structured data formats. Recent iterations, such as Gemini 2.5 Deep Think, \n", "\u001b[1;33m   \u001b[0mcan handle context windows exceeding 1 million tokens and output up to 192,000 tokens, enabling the processing  \n", "\u001b[1;33m   \u001b[0mof vast and complex documents or media streams.                                                                 \n", "\u001b[1;33m • \u001b[0m\u001b[1mAdvanced Reasoning\u001b[0m: The “Deep Think” and Pro variants deploy parallel hypothesis testing and reinforcement      \n", "\u001b[1;33m   \u001b[0mlearning, excelling at coding, complex mathematical reasoning, multimodal understanding, structured outputs, and\n", "\u001b[1;33m   \u001b[0mfunction/tool calling.                                                                                          \n", "\u001b[1;33m • \u001b[0m\u001b[1mAgentic Capabilities\u001b[0m: Gemini powers research agents and universal AI assistants (e.g., Project Astra, Project   \n", "\u001b[1;33m   \u001b[0m<PERSON><PERSON>ner), handling autonomous workflows like code debugging, real-time information retrieval, and interactive   \n", "\u001b[1;33m   \u001b[0mweb task completion.                                                                                            \n", "\u001b[1;33m • \u001b[0m\u001b[1mSafety and Governance\u001b[0m: Gemini employs extensive safety measures, including red teaming (automated and external),\n", "\u001b[1;33m   \u001b[0mmulti-tiered abuse monitoring, mitigations against sensitive domains (e.g., chemical/biological), and oversight \n", "\u001b[1;33m   \u001b[0mby Google DeepMind’s Responsibility and Safety Council. It adheres to ethical principles such as minimization of\n", "\u001b[1;33m   \u001b[0mbias and toxicity.                                                                                              \n", "\n", "                                                \u001b[1mIntended Use Cases\u001b[0m                                                 \n", "\n", "\u001b[1;33m • \u001b[0mComplex knowledge work (e.g., scientific research, coding assistants, market analysis)                          \n", "\u001b[1;33m • \u001b[0mLarge-scale document analysis, summarization, and synthesis                                                     \n", "\u001b[1;33m • \u001b[0mContent creation across text, image, video (e.g., AI-powered design, video generation with Veo, music with      \n", "\u001b[1;33m   \u001b[0mLyria)                                                                                                          \n", "\u001b[1;33m • \u001b[0mAutonomous agents for enterprise workflows (e.g., web research, spreadsheet automation, technical support,      \n", "\u001b[1;33m   \u001b[0mcreative design)                                                                                                \n", "\u001b[1;33m • \u001b[0mIntegration into Google products and services: Workspace (Docs, Sheets, Slides), Pixel devices, Search, Ads,    \n", "\u001b[1;33m   \u001b[0mChrome, and Duet AI                                                                                             \n", "\n", "                                     \u001b[1mUnderlying Technologies and Architecture\u001b[0m                                      \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mSparse Mixture-of-Experts (MoE) Transformers\u001b[0m: Efficiently routes token streams to specialist “experts,” scaling \n", "\u001b[1;33m   \u001b[0mmodel power and reducing unnecessary computation compared to dense models.                                      \n", "\u001b[1;33m • \u001b[0m\u001b[1mTraining Infrastructure\u001b[0m: Built and scaled atop Google’s custom Tensor Processing Units (TPUs), notably the      \n", "\u001b[1;33m   \u001b[0mlatest Trillium and v5p generations, offering high energy efficiency and throughput.                            \n", "\u001b[1;33m • \u001b[0m\u001b[1mData and Multimodal Alignment\u001b[0m: Gemini models are instruction-tuned on diverse datasets spanning internet text,  \n", "\u001b[1;33m   \u001b[0mcode, multimodal data, and human tool-use demonstrations, with additional reinforcement from human and AI       \n", "\u001b[1;33m   \u001b[0mfeedback loops.                                                                                                 \n", "\u001b[1;33m • \u001b[0m\u001b[1mAgentic Systems\u001b[0m: Gemini integrates tool use (code execution, search), rapid context expansion (handling very    \n", "\u001b[1;33m   \u001b[0mlarge documents), and interaction with third-party tools through its APIs.                                      \n", "\n", "                                              \u001b[1mPerformance Benchmarks\u001b[0m                                               \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mAcademic and Coding Tasks\u001b[0m:                                                                                      \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mOutperformed humans on MMLU (90.0% - Gemini Ultra) and competes at IMO (mathematics competition) Bronze level\n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m(60.7%).                                                                                                     \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mAchieves strong results on coding (LiveCodeBench 87.6%), knowledge, and reasoning benchmarks.                \n", "\u001b[1;33m • \u001b[0m\u001b[1mAgentic Tasks\u001b[0m:                                                                                                  \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mProject Mariner and Gemini Plays Pokémon showcase advanced planning, tool use, and context management, e.g., \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0mcompleting Pokémon Blue autonomously after hundreds of hours.                                                \n", "\u001b[1;33m • \u001b[0m\u001b[1mComparative Edge\u001b[0m:                                                                                               \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0m<PERSON><PERSON><PERSON> is recognized for high factual accuracy, extremely large context windows, and rapid processing,       \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0mespecially for multimodal or large-scale tasks.                                                              \n", "\n", "                                                \u001b[1mIntegration Options\u001b[0m                                                \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mAPIs\u001b[0m: Accessible via Google AI Studio, Vertex AI (for cloud deployment), and dedicated APIs (REST, SDKs in      \n", "\u001b[1;33m   \u001b[0mvarious languages).                                                                                             \n", "\u001b[1;33m • \u001b[0m\u001b[1mProduct Deep Integration\u001b[0m: Gemini is foundational to Workspace tools, Pixel on-device AI, Search Labs' AI        \n", "\u001b[1;33m   \u001b[0mOverviews, and Duet AI for enterprise productivity.                                                             \n", "\u001b[1;33m • \u001b[0m\u001b[1mDeveloper Ecosystem\u001b[0m: Offers Model Garden for testing/deployment, detailed rate limits with scalable quotas,     \n", "\u001b[1;33m   \u001b[0mbatch mode support for cost efficiency, and transparent, usage-based pricing.                                   \n", "\u001b[1;33m • \u001b[0m\u001b[1mFine-tuning & Customization\u001b[0m: Support for model customization and domain-specific tasks, with facilities for     \n", "\u001b[1;33m   \u001b[0mprompt engineering, tool chaining, and safe deployment monitoring.                                              \n", "\n", "                                         \u001b[1mNotable Strengths and Weaknesses\u001b[0m                                          \n", "\n", "\u001b[1mStrengths\u001b[0m                                                                                                          \n", "\n", "\u001b[1;33m • \u001b[0mBroad and deep multimodality with vast context (1M+ tokens)                                                     \n", "\u001b[1;33m • \u001b[0mHigh-speed inference for large, complex workflows                                                               \n", "\u001b[1;33m • \u001b[0mDeep integration across Google’s ecosystem and developer tools                                                  \n", "\u001b[1;33m • \u001b[0mIndustry-leading benchmarking on factual, reasoning, and coding tasks                                           \n", "\u001b[1;33m • \u001b[0mAdvanced safety governance                                                                                      \n", "\n", "\u001b[1mWeaknesses\u001b[0m                                                                                                         \n", "\n", "\u001b[1;33m • \u001b[0mSome known issues with hallucination and occasional latency, particularly at the highest computation tiers      \n", "\u001b[1;33m • \u001b[0mOver-refusal of benign queries due to conservative safety layers                                                \n", "\u001b[1;33m • \u001b[0mEarlier access to specialized features (e.g., newest model variants) may be gated or subject to usage           \n", "\u001b[1;33m   \u001b[0mrestrictions                                                                                                    \n", "\u001b[1;33m • \u001b[0mPremium enterprise pricing for advanced models and batch processing                                             \n", "\n", "[1][2][3][4][5][6][7][8][9][10][11][12]                                                                            \n", "\n", "\u001b[33m───────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m\n", "\n", "                                       \u001b[1;4mOverview: OpenAI Deep Research Agents\u001b[0m                                       \n", "\n", "                                                 \u001b[1mCore Capabilities\u001b[0m                                                 \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mAutonomous Multi-Step Research\u001b[0m: Deep Research agents can independently plan, browse the real-time internet,     \n", "\u001b[1;33m   \u001b[0manalyze hundreds of resources (websites, PDFs, images, structured files), synthesize findings, and generate     \n", "\u001b[1;33m   \u001b[0mcomprehensive, citation-backed research reports. Transparency is emphasized through step-by-step action logs and\n", "\u001b[1;33m   \u001b[0min-line source attribution.                                                                                     \n", "\u001b[1;33m • \u001b[0m\u001b[1mAgentic Reasoning & Workflow Automation\u001b[0m: Empowers users to delegate entire research projects, from web data     \n", "\u001b[1;33m   \u001b[0mcollection to report writing, planning, and visualization. The newly launched ChatGPT Agent combines these      \n", "\u001b[1;33m   \u001b[0mcapabilities with code execution, web automation, and third-party tool integration (e.g., creating presentations\n", "\u001b[1;33m   \u001b[0mor spreadsheets).                                                                                               \n", "\u001b[1;33m • \u001b[0m\u001b[1mCitations & Analytical Traceability\u001b[0m: For every decisive step, Deep Research logs tools invoked (web search, code\n", "\u001b[1;33m   \u001b[0minterpreters, Model Context Protocol connectors), building a traceable reasoning chain akin to an expert        \n", "\u001b[1;33m   \u001b[0<PERSON><PERSON><PERSON>’s workflow.                                                                                             \n", "\u001b[1;33m • \u001b[0m\u001b[1mSafety & User Control\u001b[0m: Implements explicit user approval for consequential actions, privacy/sandboxing for      \n", "\u001b[1;33m   \u001b[0msensitive tasks, and comprehensive risk mitigations, including prompt-injection and data exfiltration defenses. \n", "\n", "                                                \u001b[1mIntended Use Cases\u001b[0m                                                 \n", "\n", "\u001b[1;33m • \u001b[0mIn-depth market, policy, technical, or literature research requiring source-backed synthesis                    \n", "\u001b[1;33m • \u001b[0mCompetitive intelligence, product comparison, and strategy reports                                              \n", "\u001b[1;33m • \u001b[0mAutomated health economics analysis, regulatory compliance checks, and legal research                           \n", "\u001b[1;33m • \u001b[0mSpreadsheet automation, technical reporting, real-time trend and news aggregation                               \n", "\u001b[1;33m • \u001b[0mEnterprise integrations (Gmail, GitHub, financial modeling) and advanced agentic workflows                      \n", "\n", "                                     \u001b[1mUnderlying Technologies and Architecture\u001b[0m                                      \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mLarge-scale Reasoning Models\u001b[0m: Deep Research leverages optimized variants of OpenAI’s o3 and forthcoming o4      \n", "\u001b[1;33m   \u001b[0mmodels, which are highly agentic, employing transformer backbones enhanced by real-world tool-use learning and  \n", "\u001b[1;33m   \u001b[0madvanced browsing capabilities.                                                                                 \n", "\u001b[1;33m • \u001b[0m\u001b[1mReinforcement Learning on Real Tasks\u001b[0m: Trained through reinforcement learning on real browser/Python/coding      \n", "\u001b[1;33m   \u001b[0mtasks, with extensive multi-modal alignment and reasoning fine-tuning.                                          \n", "\u001b[1;33m • \u001b[0m\u001b[1mAPI Design\u001b[0m: Exposes research as an asynchronous background job, returning final structured reports with detailed\n", "\u001b[1;33m   \u001b[0mlogs; supports tool integrations (web search, code interpreter, Model Context Protocol servers for internal data\n", "\u001b[1;33m   \u001b[0mresearch).                                                                                                      \n", "\u001b[1;33m • \u001b[0m\u001b[1mPrivacy and Safety by Design\u001b[0m: Restricts certain actions, logs all tool usage, requires trusted connectors, and  \n", "\u001b[1;33m   \u001b[0menforces separation of sensitive data workflows.                                                                \n", "\n", "                                              \u001b[1mPerformance Benchmarks\u001b[0m                                               \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mAcademic and Research Benchmarks\u001b[0m:                                                                               \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mState-of-the-art on Humanity’s Last Exam (26.6% - o3; new pass@1 of 41.6 for unified ChatGPT agent) and GAIA \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0m(average accuracy of 72.6%).                                                                                 \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mWell-suited for complex, multi-modal queries requiring synthesis across external and internal data.          \n", "\u001b[1;33m • \u001b[0m\u001b[1mAnalyst-Level Workflows\u001b[0m:                                                                                        \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mDomain experts consistently rate Deep Research as automating multiple hours of challenging research per      \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0msuccessful query.                                                                                            \n", "\u001b[1;33m • \u001b[0m\u001b[1mComparative Findings\u001b[0m:                                                                                           \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m • \u001b[0mOutperforms competitors (e.g., Gemini, DeepSeek, Perplexity) on multi-step analytic depth, citation          \n", "\u001b[1;33m   \u001b[0m\u001b[1;33m   \u001b[0madherence, and complex chain-of-thought queries, albeit at higher cost and slower speeds.                    \n", "\n", "                                                \u001b[1mIntegration Options\u001b[0m                                                \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mChatGPT Interface\u001b[0m: Accessible through ChatGPT’s web and desktop/mobile apps by selecting “Deep Research” in     \n", "\u001b[1;33m   \u001b[0mmessage composition.                                                                                            \n", "\u001b[1;33m • \u001b[0m\u001b[1mAPI (Responses/Deep Research API)\u001b[0m: For developers, supports advanced integrations, automation, and tool         \n", "\u001b[1;33m   \u001b[0mchaining, including the specification of custom data connectors and prompt rewriters.                           \n", "\u001b[1;33m • \u001b[0m\u001b[1mConnectors & Third-Party Apps\u001b[0m: Proactive workflow automation via connectors to Gmail, GitHub, and enterprise    \n", "\u001b[1;33m   \u001b[0msuites, with sandboxed and permissioned real-world actions via the ChatGPT Agent.                               \n", "\u001b[1;33m • \u001b[0m\u001b[1mPricing & Rate Limits\u001b[0m: Currently available to Pro, Plus, Team, and Enterprise users, with expansion to the Free \n", "\u001b[1;33m   \u001b[0mtier planned. The service is compute-intensive and billed at a premium (approx. $200/month), with lower-latency \n", "\u001b[1;33m   \u001b[0mvariants and higher rate limits in development.                                                                 \n", "\n", "                                         \u001b[1mNotable Strengths and Weaknesses\u001b[0m                                          \n", "\n", "\u001b[1mStrengths\u001b[0m                                                                                                          \n", "\n", "\u001b[1;33m • \u001b[0mHighest depth for source-rich, well-cited analytic research                                                     \n", "\u001b[1;33m • \u001b[0mIterative refinement and “human assistant” transparency in reasoning and decision logging                       \n", "\u001b[1;33m • \u001b[0mUnified agentic system bridges complex research, web automation, and coding actions                             \n", "\u001b[1;33m • \u001b[0mRobust safety (control over actions, explicit approvals, privacy isolation)                                     \n", "\u001b[1;33m • \u001b[0mBroad integration, especially for knowledge-intensive and enterprise research tasks                             \n", "\n", "\u001b[1mWeaknesses\u001b[0m                                                                                                         \n", "\n", "\u001b[1;33m • \u001b[0mProcessing time per research job (5–30 minutes) is substantially higher than general-purpose LLM outputs        \n", "\u001b[1;33m • \u001b[0mCost is significantly higher than competitors for in-depth research                                             \n", "\u001b[1;33m • \u001b[0mOccasional hallucinations, confidence calibration issues, and variable output formatting                        \n", "\u001b[1;33m • \u001b[0mResearch is most effective with fully-specified, well-formed prompts (API expects little to no clarification)   \n", "\n", "[1][2][3][5][6][7][8]                                                                                              \n", "\n", "\u001b[33m───────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m\n", "\n", "                                               \u001b[1;4mComparative Analysis\u001b[0m                                                \n", "\n", "                                   \u001b[1mCore Capabilities and Use Case Specialization\u001b[0m                                   \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mGemini\u001b[0m excels as a general-purpose multimodal platform, rapidly handling diverse inputs (including extremely    \n", "\u001b[1;33m   \u001b[0mlong texts, images, video, and audio) with strong reasoning and large context support. Its agentic variants (2.5\n", "\u001b[1;33m   \u001b[0mDeep Think) enable advanced planning and autonomy in complex workflows.                                         \n", "\u001b[1;33m • \u001b[0m\u001b[1mDeep Research\u001b[0m is focused on deep, source-cited analytic synthesis—essentially automating the high-end research  \n", "\u001b[1;33m   \u001b[0manalyst’s workflow with maximal coverage, transparency, and reasoning traceability. Its agentic strengths shine \n", "\u001b[1;33m   \u001b[0min chaining multi-step queries, executing code, and integrating findings across disparate domains.              \n", "\n", "                                           \u001b[1mArchitecture and Model Design\u001b[0m                                           \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mGemini\u001b[0m is built on sparse Mixture-of-Experts architecture for scalability and efficiency, and is natively       \n", "\u001b[1;33m   \u001b[0mmultimodal from the ground up. It is deeply embedded in Google’s infrastructure (TPUs, Workspace, Pixel).       \n", "\u001b[1;33m • \u001b[0m\u001b[1mDeep Research\u001b[0m layers agentic reasoning atop advanced transformer backbones (optimized o3/o4), employs           \n", "\u001b[1;33m   \u001b[0mreinforcement learning on tool-use tasks, and exposes fine-grained control and visibility via OpenAI’s API.     \n", "\n", "                                           \u001b[1mPerformance and Benchmarking\u001b[0m                                            \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mG<PERSON>ini\u001b[0m has benchmark leadership in large context, speed, and multimodal deployments (e.g., hours of video,      \n", "\u001b[1;33m   \u001b[0mcomplex coding, factual QA), and generally edges out competitors for tasks blending modality and context length.\n", "\u001b[1;33m • \u001b[0m\u001b[1mDeep Research\u001b[0m leads on traditional research metrics—multi-step logical synthesis, depth of citation, and        \n", "\u001b[1;33m   \u001b[0manalyst-level report creation—as reflected in domain expert evaluations and results on reasoning-heavy          \n", "\u001b[1;33m   \u001b[0mbenchmarks (Humanity’s Last Exam, GAIA).                                                                        \n", "\u001b[1;33m • \u001b[0m\u001b[1mCaveat on Benchmarks\u001b[0m: Both Google and OpenAI acknowledge the limitations of current benchmarks, with issues     \n", "\u001b[1;33m   \u001b[0maround dataset validity, saturation, and safety signaling. Users should regard reported scores as indicative but\n", "\u001b[1;33m   \u001b[0mnot absolute measures of real-world performance ([9]).                                                          \n", "\n", "                                             \u001b[1mIntegration and Ecosystem\u001b[0m                                             \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mGemini\u001b[0m is highly accessible for developers and enterprises, offering mature APIs, Workspace integrations, and   \n", "\u001b[1;33m   \u001b[0mready deployment via Vertex AI with transparent pricing and extensive documentation. It supports fine-tuning,   \n", "\u001b[1;33m   \u001b[0mcustomization, and flexible deployment models (cloud, on-device).                                               \n", "\u001b[1;33m • \u001b[0m\u001b[1mDeep Research\u001b[0m is primarily available in ChatGPT and as an API for advanced developer use. Its workflow          \n", "\u001b[1;33m   \u001b[0mcapabilities are exposed via OpenAI's agent platform (ChatGPT Agent) and connectors, focusing on                \n", "\u001b[1;33m   \u001b[0mknowledge-intensive enterprise environments and technical research.                                             \n", "\n", "                                           \u001b[1mCost, Access, and Scalability\u001b[0m                                           \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mGemini\u001b[0m offers broader cost tiers (including accessible entry points and enterprise pricing) and generous        \n", "\u001b[1;33m   \u001b[0mfree/developer access for basic use. Batch processing and context caching are available at reduced rates for    \n", "\u001b[1;33m   \u001b[0mlarge-volume research.                                                                                          \n", "\u001b[1;33m • \u001b[0m\u001b[1mDeep Research\u001b[0m is positioned as a premium solution, with higher per-query costs and longer compute times. Rollout\n", "\u001b[1;33m   \u001b[0mto broader user tiers and cost-optimized models is in progress.                                                 \n", "\n", "                                         \u001b[1mStrengths and Weaknesses Overview\u001b[0m                                         \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "                                                                                                                   \n", " \u001b[1m \u001b[0m\u001b[1m          \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mGoogle Gemini\u001b[0m\u001b[1m                                  \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mOpenAI Deep Research\u001b[0m\u001b[1m                            \u001b[0m\u001b[1m \u001b[0m \n", " ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ \n", "  \u001b[1mStrengths\u001b[0m    - Largest context, multimodal integration-        - Unmatched depth in citation-rich, analytic      \n", "               Speed, deep Google product integration- Strong    synthesis- Transparent, step-wise reasoning &     \n", "               factual accuracy and enterprise scalability-      action logs- Agentic research merges              \n", "               Comprehensive safety governance                   web/data/code tools- Enterprise/analyst           \n", "                                                                 automation excellence                             \n", "  \u001b[1mWeaknesses\u001b[0m   - Occasional slowdowns at scale- Over-refusal     - High cost, slow processing per job-             \n", "               conservatism - Some feature gating- Top-tier      Hallucinations/confidence issues- Prompts must    \n", "               pricing for advanced models                       be highly specific- Format/UX issues in some      \n", "                                                                 outputs                                           \n", "                                                                                                                   \n", "\n", "\u001b[33m───────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m\n", "\n", "                                                    \u001b[1;4mConclusion\u001b[0m                                                     \n", "\n", "Google Gemini and OpenAI’s Deep Research agents, while both representing the cutting edge of AI, are tailored to   \n", "different segments of the knowledge-work and agent automation spectrum:                                            \n", "\n", "\u001b[1;33m • \u001b[0m\u001b[1mUse Gemini\u001b[0m if you require fast, scalable, multimodal reasoning and content generation, integration with Google’s\n", "\u001b[1;33m   \u001b[0msuite of tools, support for massive context windows, and broad developer deployment.                            \n", "\u001b[1;33m • \u001b[0m\u001b[1mUse Deep Research\u001b[0m when you need the highest level of analytic synthesis, traceability, and source-backed        \n", "\u001b[1;33m   \u001b[0mreporting—essential for research analysts, legal/technical experts, and enterprises that require auditable,     \n", "\u001b[1;33m   \u001b[0mmulti-step automation and actionability.                                                                        \n", "\n", "Both platforms are converging towards more capable agentic AI, integrating multi-step reasoning, workflow          \n", "automation, and external tool use. User choice should be guided by the project’s required depth, speed, modality,  \n", "integration needs, and budget constraints.                                                                         \n", "\n", "\u001b[33m───────────────────────────────────────────────────────────────────────────────────────────────────────────────────\u001b[0m\n", "\n", "                                                      \u001b[1;4mSources\u001b[0m                                                      \n", "\n", "\u001b[1;33m  1 \u001b[0m\u001b]8;id=817553;https://ai.google.dev/gemini-api/docs/models\u001b\\\u001b[4;34mGemini models | Gemini API | Google AI for Developers\u001b[0m\u001b]8;;\u001b\\                                                          \n", "\u001b[1;33m  2 \u001b[0m\u001b]8;id=25456;https://blog.google/technology/ai/google-gemini-ai/\u001b\\\u001b[4;34mIntroducing Gemini: our largest and most capable AI model\u001b[0m\u001b]8;;\u001b\\                                                      \n", "\u001b[1;33m  3 \u001b[0m\u001b]8;id=474110;https://cloud.google.com/vertex-ai/generative-ai/docs/models\u001b\\\u001b[4;34mGoogle models | Generative AI on Vertex AI\u001b[0m\u001b]8;;\u001b\\                                                                     \n", "\u001b[1;33m  4 \u001b[0m\u001b]8;id=353101;https://storage.googleapis.com/deepmind-media/Model-Cards/Gemini-2-5-Deep-Think-Model-Card.pdf\u001b\\\u001b[4;34mGemini 2.5 Deep Think - Model Card - Googleapis.com\u001b[0m\u001b]8;;\u001b\\                                                            \n", "\u001b[1;33m  5 \u001b[0m\u001b]8;id=31685;https://blog.google/technology/google-deepmind/google-gemini-ai-update-december-2024/\u001b\\\u001b[4;34mIntroducing Gemini 2.0: our new AI model for the agentic era\u001b[0m\u001b]8;;\u001b\\                                                   \n", "\u001b[1;33m  6 \u001b[0m\u001b]8;id=306018;https://storage.googleapis.com/deepmind-media/gemini/gemini_v2_5_report.pdf\u001b\\\u001b[4;34mGemini 2.5: Pushing the Frontier with Advanced Reasoning ...\u001b[0m\u001b]8;;\u001b\\                                                   \n", "\u001b[1;33m  7 \u001b[0m\u001b]8;id=206319;https://newo.ai/insights/google-gemini-vs-chatgpt-a-comprehensive-api-comparison/\u001b\\\u001b[4;34mComparing Google Gemini and ChatGPT: API Keys, Costs & Access\u001b[0m\u001b]8;;\u001b\\                                                  \n", "\u001b[1;33m  8 \u001b[0m\u001b]8;id=849298;https://www.datastudios.org/post/chatgpt-vs-claude-vs-gemini-full-report-and-comparison-of-features-performance-integrations-pric\u001b\\\u001b[4;34mChatGPT vs Claude vs Gemini: Full Report and Comparison of ...\u001b[0m\u001b]8;;\u001b\\                                                 \n", "\u001b[1;33m  9 \u001b[0m\u001b]8;id=752024;https://creatoreconomy.so/p/chatgpt-vs-claude-vs-gemini-the-best-ai-model-for-each-use-case-2025\u001b\\\u001b[4;34mChatGPT vs Claude vs Gemini: The Best AI Model for Each Use ...\u001b[0m\u001b]8;;\u001b\\                                                \n", "\u001b[1;33m 10 \u001b[0m\u001b]8;id=726711;https://ai.google.dev/gemini-api/docs/rate-limits\u001b\\\u001b[4;34mRate limits | Gemini API | Google AI for Developers\u001b[0m\u001b]8;;\u001b\\                                                            \n", "\u001b[1;33m 11 \u001b[0m\u001b]8;id=187079;https://ai.google.dev/gemini-api/docs/pricing\u001b\\\u001b[4;34mGemini Developer API Pricing | Gemini API | Google AI for Developers\u001b[0m\u001b]8;;\u001b\\                                           \n", "\u001b[1;33m 12 \u001b[0m\u001b]8;id=261667;https://cloud.google.com/vertex-ai/generative-ai/pricing\u001b\\\u001b[4;34mVertex AI Pricing | Generative AI on Vertex AI - Google Cloud\u001b[0m\u001b]8;;\u001b\\                                                  \n", "\u001b[1;33m 13 \u001b[0m\u001b]8;id=199900;https://openai.com/index/introducing-deep-research/\u001b\\\u001b[4;34mIntroducing deep research - OpenAI\u001b[0m\u001b]8;;\u001b\\                                                                             \n", "\u001b[1;33m 14 \u001b[0m\u001b]8;id=796568;https://openai.com/index/introducing-chatgpt-agent/\u001b\\\u001b[4;34mIntroducing ChatGPT agent: bridging research and action - OpenAI\u001b[0m\u001b]8;;\u001b\\                                               \n", "\u001b[1;33m 15 \u001b[0m\u001b]8;id=26590;https://bytebridge.medium.com/openais-deep-research-tool-a-comprehensive-overview-12ddab43feff\u001b\\\u001b[4;34mOpenAI's Deep Research Tool: A Comprehensive Overview\u001b[0m\u001b]8;;\u001b\\                                                          \n", "\u001b[1;33m 16 \u001b[0m\u001b]8;id=637718;https://platform.openai.com/docs/guides/deep-research\u001b\\\u001b[4;34mDeep research - OpenAI API\u001b[0m\u001b]8;;\u001b\\                                                                                     \n", "\u001b[1;33m 17 \u001b[0m\u001b]8;id=755889;https://cookbook.openai.com/examples/deep_research_api/introduction_to_deep_research_api\u001b\\\u001b[4;34mIntroduction to deep research in the OpenAI API\u001b[0m\u001b]8;;\u001b\\                                                                \n", "\u001b[1;33m 18 \u001b[0m\u001b]8;id=659639;https://www.ai-supremacy.com/p/openai-deep-research-vs-google-deep\u001b\\\u001b[4;34mOpenAI Deep Research vs. Google Deep Research - AI Supremacy\u001b[0m\u001b]8;;\u001b\\                                                   \n", "\u001b[1;33m 19 \u001b[0m\u001b]8;id=603946;https://www.helicone.ai/blog/openai-deep-research\u001b\\\u001b[4;34mOpenAI Deep Research: How it Compares to Perplexity and Gemini\u001b[0m\u001b]8;;\u001b\\                                                 \n", "\u001b[1;33m 20 \u001b[0m\u001b]8;id=110829;https://arxiv.org/html/2502.06559v2\u001b\\\u001b[4;34mCan We Trust AI Benchmarks? An Interdisciplinary Review of ... - arXiv\u001b[0m\u001b]8;;\u001b\\                                         \n"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from rich.markdown import Markdown\n", "Markdown(result[\"final_report\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see the trace [here](https://smith.langchain.com/public/86ebdc25-4595-4040-be1e-a8e30052786b/r).\n", "\n", "### LangGraph Studio\n", "\n", "Just as we did before, we can also use LangGraph Studio to visualize the agent. \n", "\n", "The files we wrote with `%%writefile` to `src/deep_research_from_scratch/` during the all the notebooks create the files for our application:\n", "\n", "```\n", "deep_research_from_scratch/\n", "├── src/deep_research_from_scratch/\n", "│   ├── state.py          # State definitions\n", "│   ├── scope_research.py # Scoping workflow\n", "│   ├── prompts.py        # Prompt templates\n", "│   └── ...\n", "├── notebooks/            # Development notebooks\n", "├── pyproject.toml        # Dependencies\n", "└── langgraph.json        # LangGraph configuration\n", "```\n", "\n", "This agent has been added to the `langgraph.json` file, so you can select `research_agent_full` in the dropdown menu:\n", "\n", "```\n", "\"research_agent_full\": \"./src/deep_research_from_scratch/research_agent_full.py:agent\"\n", "```\n", "\n", "Run the following command to start the studio\n", "\n", "```bash\n", "uvx --refresh --from \"langgraph-cli[inmem]\" --with-editable . --python 3.11 langgraph dev --allow-blocking\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 4}