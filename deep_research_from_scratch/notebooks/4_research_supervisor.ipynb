{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["# Load environment variables and set up auto-reload\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"attachments": {"834d54a0-26dc-48fc-bc7f-39713ebf5396.webp": {"image/webp": "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"}, "cac50ae6-f10d-4f40-8604-d4b3244628ed.webp": {"image/webp": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# Research Supervisor\n", "\n", "*The supervisor has a simple job: delegate research tasks to an appropriate number of sub-agents.*\n", "\n", "Here is our overall research flow:\n", "\n", "![image.webp](attachment:834d54a0-26dc-48fc-bc7f-39713ebf5396.webp)\n", "\n", "We previously built a research agent bound to either custom tools or MCP server. Now, what happens if the request is complex and has several sub-topics? Single agent response quality *can* suffer with multiple sub-topics (e.g., compare A to B to C) because a single context window needs to store and reason about tool feedback across all of the sub-topics. \n", "\n", "[Numerous failure modes](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html), such as [context clash](https://www.dbreunig.com/2025/06/22/how-contexts-fail-and-how-to-fix-them.html), become prevalent as the context window accumulates tool calls across many different sub-topics. As discussed in [<PERSON><PERSON><PERSON>'s blog post](https://www.anthropic.com/engineering/built-multi-agent-research-system), multi-agent systems can split sub-topic to sub-agents with isolated context windows. We'll build a system where a supervisor determines if the research brief can be broken-down into independent sub-topics and delegates to sub-agents with isolated context windows. \n", "\n", "![image.webp](attachment:cac50ae6-f10d-4f40-8604-d4b3244628ed.webp)\n", "\n", "### Prompt\n", "\n", "Now, let's design a prompt for the supervisor that follows the principles we've discussed, and include some insights from [the research literature](https://www.anthropic.com/engineering/built-multi-agent-research-system).\n", "\n", "#### 1. Think Like The Agent\n", "What instructions would you give a new work colleague?\n", "- **Read the question carefully** - What specific information does the user need?\n", "- **Decide how to delegate the research** - Carefully consider the question and decide how to delegate the research. Are there multiple independent directions that can be explored simultaneously?\n", "- **After each call to Conduct<PERSON><PERSON><PERSON><PERSON>, pause and assess** - Do I have enough to answer? What's still missing?\n", "\n", "#### 2. Concrete Heuristics (For task delegation)\n", "Use **Hard Limits** to prevent the research agent from calling tools excessively:\n", "- **Bias towards single agent** - Use single agent for simplicity unless the user request has clear opportunity for parallelization. \n", "- **Stop when you can answer confidently** - Don't keep delegating research for perfection.\n", "- **Limit tool calls** - Always stop after 3 tool calls to ConductResearch if you cannot find the right source(s).\n", "\n", "#### 3. Show your thinking\n", "Before you call ConductResearch tool call, use think_tool to plan your approach:\n", "- Can the task be broken down into smaller sub-tasks?\n", "\n", "After each ConductResearch tool call, use think_tool to analyze the results:\n", "- What key information did I find? \n", "- What's missing?\n", "- Do I have enough to answer the question comprehensively?\n", "- Should I search more or provide my answer?\n", "\n", "#### 4. <PERSON><PERSON> rules\n", "Simple fact-finding, lists, and rankings can use a single sub-agent.\n", "- *Example*: List the top 10 coffee shops in San Francisco -> Use 1 sub-agent\n", "\n", "Comparisons presented in the user request can use a sub-agent for each element of the comparison.\n", "- *Example*: Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety -> Use 3 sub-agents.\n", "- Delegate clear, distinct, non-overlapping subtopics"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Lead Researcher Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You are a research supervisor. Your job is to conduct research by calling the \"ConductResearch\" tool. For      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  context, today's date is {date}.                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Task&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Your focus is to call the \"ConductResearch\" tool to conduct research against the overall research question     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  passed in by the user.                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  When you are completely satisfied with the research findings returned from the tool calls, then you should     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  call the \"ResearchComplete\" tool to indicate that you are done with your research.                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Task&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Available Tools&gt;</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You have access to three main tools:                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **ConductResearch**: Delegate research tasks to specialized sub-agents                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **ResearchComplete**: Indicate that research is complete                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. **think_tool**: For reflection and strategic planning during research                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **CRITICAL: Use think_tool before calling ConductResearch to plan your approach, and after each                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  ConductResearch to assess progress**                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **PARALLEL RESEARCH**: When you identify multiple independent sub-topics that can be explored simultaneously,  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  make multiple ConductResearch tool calls in a single response to enable parallel research execution. This is   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  more efficient than sequential research for comparative or multi-faceted questions. Use at most                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {max_concurrent_research_units} parallel agents per iteration.                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Available Tools&gt;</span>                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Instructions&gt;</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Think like a research manager with limited time and resources. Follow these steps:                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **Read the question carefully** - What specific information does the user need?                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **Decide how to delegate the research** - Carefully consider the question and decide how to delegate the    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  research. Are there multiple independent directions that can be explored simultaneously?                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. **After each call to Conduct<PERSON><PERSON>ar<PERSON>, pause and assess** - Do I have enough to answer? What's still         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  missing?                                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Instructions&gt;</span>                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Hard Limits&gt;</span>                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Task Delegation Budgets** (Prevent excessive delegation):                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Bias towards single agent** - Use single agent for simplicity unless the user request has clear            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  opportunity for parallelization                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Stop when you can answer confidently** - Don't keep delegating research for perfection                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Limit tool calls** - Always stop after {max_researcher_iterations} tool calls to think_tool and            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  ConductResearch if you cannot find the right sources                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Hard Limits&gt;</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Show Your Thinking&gt;</span>                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Before you call ConductResearch tool call, use think_tool to plan your approach:                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Can the task be broken down into smaller sub-tasks?                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  After each ConductResearch tool call, use think_tool to analyze the results:                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - What key information did I find?                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - What's missing?                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Do I have enough to answer the question comprehensively?                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Should I delegate more research or call ResearchComplete?                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Show Your Thinking&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Scaling Rules&gt;</span>                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Simple fact-finding, lists, and rankings** can use a single sub-agent:                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: List the top 10 coffee shops in San Francisco → Use 1 sub-agent                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Comparisons presented in the user request** can use a sub-agent for each element of the comparison:          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety → Use 3 sub-agents              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Delegate clear, distinct, non-overlapping subtopics                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Important Reminders:**                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Each ConductResearch call spawns a dedicated research agent for that specific topic                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - A separate agent will write the final report - you just need to gather information                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - When calling ConductResearch, provide complete standalone instructions - sub-agents can't see other agents'  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  work                                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Do NOT use acronyms or abbreviations in your research questions, be very clear and specific                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Scaling Rules&gt;</span>                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mLead Researcher Prompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You are a research supervisor. Your job is to conduct research by calling the \"ConductResearch\" tool. For      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  context, today's date is {date}.                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Task>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Your focus is to call the \"ConductResearch\" tool to conduct research against the overall research question     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  passed in by the user.                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  When you are completely satisfied with the research findings returned from the tool calls, then you should     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  call the \"ResearchComplete\" tool to indicate that you are done with your research.                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Task>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Available Tools>\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You have access to three main tools:                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **ConductResearch**: Delegate research tasks to specialized sub-agents                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **ResearchComplete**: Indicate that research is complete                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. **think_tool**: For reflection and strategic planning during research                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **CRITICAL: Use think_tool before calling ConductResearch to plan your approach, and after each                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  ConductResearch to assess progress**                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **PARALLEL RESEARCH**: When you identify multiple independent sub-topics that can be explored simultaneously,  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  make multiple ConductResearch tool calls in a single response to enable parallel research execution. This is   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  more efficient than sequential research for comparative or multi-faceted questions. Use at most                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {max_concurrent_research_units} parallel agents per iteration.                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Available Tools>\u001b[0m                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Instructions>\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Think like a research manager with limited time and resources. Follow these steps:                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **Read the question carefully** - What specific information does the user need?                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **Decide how to delegate the research** - Carefully consider the question and decide how to delegate the    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  research. Are there multiple independent directions that can be explored simultaneously?                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. **After each call to Conduct<PERSON><PERSON><PERSON><PERSON>, pause and assess** - Do I have enough to answer? What's still         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  missing?                                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Instructions>\u001b[0m                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Hard Limits>\u001b[0m                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Task Delegation Budgets** (Prevent excessive delegation):                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Bias towards single agent** - Use single agent for simplicity unless the user request has clear            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  opportunity for parallelization                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Stop when you can answer confidently** - Don't keep delegating research for perfection                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Limit tool calls** - Always stop after {max_researcher_iterations} tool calls to think_tool and            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  ConductResearch if you cannot find the right sources                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Hard Limits>\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Show Your Thinking>\u001b[0m                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Before you call ConductResearch tool call, use think_tool to plan your approach:                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Can the task be broken down into smaller sub-tasks?                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  After each ConductResearch tool call, use think_tool to analyze the results:                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - What key information did I find?                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - What's missing?                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Do I have enough to answer the question comprehensively?                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Should I delegate more research or call ResearchComplete?                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Show Your Thinking>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Scaling Rules>\u001b[0m                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Simple fact-finding, lists, and rankings** can use a single sub-agent:                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: List the top 10 coffee shops in San Francisco → Use 1 sub-agent                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Comparisons presented in the user request** can use a sub-agent for each element of the comparison:          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety → Use 3 sub-agents              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Delegate clear, distinct, non-overlapping subtopics                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Important Reminders:**                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Each ConductResearch call spawns a dedicated research agent for that specific topic                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - A separate agent will write the final report - you just need to gather information                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - When calling ConductResearch, provide complete standalone instructions - sub-agents can't see other agents'  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  work                                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Do NOT use acronyms or abbreviations in your research questions, be very clear and specific                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Scaling Rules>\u001b[0m                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import show_prompt\n", "from deep_research_from_scratch.prompts import lead_researcher_prompt\n", "show_prompt(lead_researcher_prompt, \"Lead Researcher Prompt\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### State\n", "\n", "The supervisor state manages the overall research coordination, while the researcher state handles individual research tasks."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_research_from_scratch/state_multi_agent_supervisor.py\n"]}], "source": ["%%writefile ../src/deep_research_from_scratch/state_multi_agent_supervisor.py\n", "\n", "\"\"\"\n", "State Definitions for Multi-Agent Research Supervisor\n", "\n", "This module defines the state objects and tools used for the multi-agent\n", "research supervisor workflow, including coordination state and research tools.\n", "\"\"\"\n", "\n", "import operator\n", "from typing_extensions import Annotated, TypedDict, Sequence\n", "\n", "from langchain_core.messages import BaseMessage\n", "from langchain_core.tools import tool\n", "from langgraph.graph.message import add_messages\n", "from pydantic import BaseModel, Field\n", "\n", "class SupervisorState(TypedDict):\n", "    \"\"\"\n", "    State for the multi-agent research supervisor.\n", "    \n", "    Manages coordination between supervisor and research agents, tracking\n", "    research progress and accumulating findings from multiple sub-agents.\n", "    \"\"\"\n", "    \n", "    # Messages exchanged with supervisor for coordination and decision-making\n", "    supervisor_messages: Annotated[Sequence[BaseMessage], add_messages]\n", "    # Detailed research brief that guides the overall research direction\n", "    research_brief: str\n", "    # Processed and structured notes ready for final report generation\n", "    notes: Annotated[list[str], operator.add] = []\n", "    # Counter tracking the number of research iterations performed\n", "    research_iterations: int = 0\n", "    # Raw unprocessed research notes collected from sub-agent research\n", "    raw_notes: Annotated[list[str], operator.add] = []\n", "\n", "@tool\n", "class ConductResearch(BaseModel):\n", "    \"\"\"Tool for delegating a research task to a specialized sub-agent.\"\"\"\n", "    research_topic: str = Field(\n", "        description=\"The topic to research. Should be a single topic, and should be described in high detail (at least a paragraph).\",\n", "    )\n", "\n", "@tool\n", "class ResearchComplete(BaseModel):\n", "    \"\"\"Tool for indicating that the research process is complete.\"\"\"\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multi-agent \n", "\n", "Now, we'll define our agent. A multi-agent system is a system that consists of multiple agents that work together to complete a task. The main benefit is context isolated, as discussed in [Context Engineering for Agents](https://blog.langchain.com/context-engineering-for-agents/). "]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_research_from_scratch/multi_agent_supervisor.py\n"]}], "source": ["%%writefile ../src/deep_research_from_scratch/multi_agent_supervisor.py\n", "\n", "\"\"\"Multi-agent supervisor for coordinating research across multiple specialized agents.\n", "\n", "This module implements a supervisor pattern where:\n", "1. A supervisor agent coordinates research activities and delegates tasks\n", "2. Multiple researcher agents work on specific sub-topics independently\n", "3. Results are aggregated and compressed for final reporting\n", "\n", "The supervisor uses parallel research execution to improve efficiency while\n", "maintaining isolated context windows for each research topic.\n", "\"\"\"\n", "\n", "import asyncio\n", "\n", "from typing_extensions import Literal\n", "\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.messages import (\n", "    HumanMessage, \n", "    BaseMessage, \n", "    SystemMessage, \n", "    ToolMessage,\n", "    filter_messages\n", ")\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.types import Command\n", "\n", "from deep_research_from_scratch.prompts import lead_researcher_prompt\n", "from deep_research_from_scratch.research_agent import researcher_agent\n", "from deep_research_from_scratch.state_multi_agent_supervisor import (\n", "    SupervisorState, \n", "    ConductR<PERSON><PERSON><PERSON>, \n", "    ResearchComplete\n", ")\n", "from deep_research_from_scratch.utils import get_today_str, think_tool\n", "\n", "def get_notes_from_tool_calls(messages: list[BaseMessage]) -> list[str]:\n", "    \"\"\"Extract research notes from ToolMessage objects in supervisor message history.\n", "    \n", "    This function retrieves the compressed research findings that sub-agents\n", "    return as ToolMessage content. When the supervisor delegates research to\n", "    sub-agents via ConductResearch tool calls, each sub-agent returns its\n", "    compressed findings as the content of a ToolMessage. This function\n", "    extracts all such ToolMessage content to compile the final research notes.\n", "    \n", "    Args:\n", "        messages: List of messages from supervisor's conversation history\n", "        \n", "    Returns:\n", "        List of research note strings extracted from ToolMessage objects\n", "    \"\"\"\n", "    return [tool_msg.content for tool_msg in filter_messages(messages, include_types=\"tool\")]\n", "\n", "# Ensure async compatibility for Jupyter environments\n", "try:\n", "    import nest_asyncio\n", "    # Only apply if running in Jupyter/IPython environment\n", "    try:\n", "        from IPython import get_ipython\n", "        if get_ipython() is not None:\n", "            nest_asyncio.apply()\n", "    except ImportError:\n", "        pass  # Not in Jupyter, no need for nest_asyncio\n", "except ImportError:\n", "    pass  # nest_asyncio not available, proceed without it\n", "\n", "\n", "# ===== CONFIGURATION =====\n", "\n", "supervisor_tools = [ConductResearch, ResearchComplete, think_tool]\n", "supervisor_model = init_chat_model(model=\"anthropic:claude-sonnet-4-20250514\")\n", "supervisor_model_with_tools = supervisor_model.bind_tools(supervisor_tools)\n", "\n", "# System constants\n", "# Maximum number of tool call iterations for individual researcher agents\n", "# This prevents infinite loops and controls research depth per topic\n", "max_researcher_iterations = 6 # Calls to think_tool + ConductResearch\n", "\n", "# Maximum number of concurrent research agents the supervisor can launch\n", "# This is passed to the lead_researcher_prompt to limit parallel research tasks\n", "max_concurrent_researchers = 3\n", "\n", "# ===== SUPERVISOR NODES =====\n", "\n", "async def supervisor(state: SupervisorState) -> Command[Literal[\"supervisor_tools\"]]:\n", "    \"\"\"Coordinate research activities.\n", "    \n", "    Analyzes the research brief and current progress to decide:\n", "    - What research topics need investigation\n", "    - Whether to conduct parallel research\n", "    - When research is complete\n", "    \n", "    Args:\n", "        state: Current supervisor state with messages and research progress\n", "        \n", "    Returns:\n", "        Command to proceed to supervisor_tools node with updated state\n", "    \"\"\"\n", "    supervisor_messages = state.get(\"supervisor_messages\", [])\n", "    \n", "    # Prepare system message with current date and constraints\n", "    system_message = lead_researcher_prompt.format(\n", "        date=get_today_str(), \n", "        max_concurrent_research_units=max_concurrent_researchers,\n", "        max_researcher_iterations=max_researcher_iterations\n", "    )\n", "    messages = [SystemMessage(content=system_message)] + supervisor_messages\n", "    \n", "    # Make decision about next research steps\n", "    response = await supervisor_model_with_tools.ainvoke(messages)\n", "    \n", "    return Command(\n", "        goto=\"supervisor_tools\",\n", "        update={\n", "            \"supervisor_messages\": [response],\n", "            \"research_iterations\": state.get(\"research_iterations\", 0) + 1\n", "        }\n", "    )\n", "\n", "async def supervisor_tools(state: SupervisorState) -> Command[Literal[\"supervisor\", \"__end__\"]]:\n", "    \"\"\"Execute supervisor decisions - either conduct research or end the process.\n", "    \n", "    Handles:\n", "    - Executing think_tool calls for strategic reflection\n", "    - Launching parallel research agents for different topics\n", "    - Aggregating research results\n", "    - Determining when research is complete\n", "    \n", "    Args:\n", "        state: Current supervisor state with messages and iteration count\n", "        \n", "    Returns:\n", "        Command to continue supervision, end process, or handle errors\n", "    \"\"\"\n", "    supervisor_messages = state.get(\"supervisor_messages\", [])\n", "    research_iterations = state.get(\"research_iterations\", 0)\n", "    most_recent_message = supervisor_messages[-1]\n", "    \n", "    # Initialize variables for single return pattern\n", "    tool_messages = []\n", "    all_raw_notes = []\n", "    next_step = \"supervisor\"  # De<PERSON>ult next step\n", "    should_end = False\n", "    \n", "    # Check exit criteria first\n", "    exceeded_iterations = research_iterations >= max_researcher_iterations\n", "    no_tool_calls = not most_recent_message.tool_calls\n", "    research_complete = any(\n", "        tool_call[\"name\"] == \"ResearchComplete\" \n", "        for tool_call in most_recent_message.tool_calls\n", "    )\n", "    \n", "    if exceeded_iterations or no_tool_calls or research_complete:\n", "        should_end = True\n", "        next_step = END\n", "    \n", "    else:\n", "        # Execute ALL tool calls before deciding next step\n", "        try:\n", "            # Separate think_tool calls from ConductResearch calls\n", "            think_tool_calls = [\n", "                tool_call for tool_call in most_recent_message.tool_calls \n", "                if tool_call[\"name\"] == \"think_tool\"\n", "            ]\n", "            \n", "            conduct_research_calls = [\n", "                tool_call for tool_call in most_recent_message.tool_calls \n", "                if tool_call[\"name\"] == \"ConductResearch\"\n", "            ]\n", "\n", "            # Handle think_tool calls (synchronous)\n", "            for tool_call in think_tool_calls:\n", "                observation = think_tool.invoke(tool_call[\"args\"])\n", "                tool_messages.append(\n", "                    ToolMessage(\n", "                        content=observation,\n", "                        name=tool_call[\"name\"],\n", "                        tool_call_id=tool_call[\"id\"]\n", "                    )\n", "                )\n", "\n", "            # Handle ConductResearch calls (asynchronous)\n", "            if conduct_research_calls:\n", "                # Launch parallel research agents\n", "                coros = [\n", "                    researcher_agent.ainvoke({\n", "                        \"researcher_messages\": [\n", "                            HumanMessage(content=tool_call[\"args\"][\"research_topic\"])\n", "                        ],\n", "                        \"research_topic\": tool_call[\"args\"][\"research_topic\"]\n", "                    }) \n", "                    for tool_call in conduct_research_calls\n", "                ]\n", "\n", "                # Wait for all research to complete\n", "                tool_results = await asyncio.gather(*coros)\n", "\n", "                # Format research results as tool messages\n", "                # Each sub-agent returns compressed research findings in result[\"compressed_research\"]\n", "                # We write this compressed research as the content of a ToolMessage, which allows\n", "                # the supervisor to later retrieve these findings via get_notes_from_tool_calls()\n", "                research_tool_messages = [\n", "                    ToolMessage(\n", "                        content=result.get(\"compressed_research\", \"Error synthesizing research report\"),\n", "                        name=tool_call[\"name\"],\n", "                        tool_call_id=tool_call[\"id\"]\n", "                    ) for result, tool_call in zip(tool_results, conduct_research_calls)\n", "                ]\n", "                \n", "                tool_messages.extend(research_tool_messages)\n", "\n", "                # Aggregate raw notes from all research\n", "                all_raw_notes = [\n", "                    \"\\n\".join(result.get(\"raw_notes\", [])) \n", "                    for result in tool_results\n", "                ]\n", "                \n", "        except Exception as e:\n", "            print(f\"Error in supervisor tools: {e}\")\n", "            should_end = True\n", "            next_step = END\n", "    \n", "    # Single return point with appropriate state updates\n", "    if should_end:\n", "        return Command(\n", "            goto=next_step,\n", "            update={\n", "                \"notes\": get_notes_from_tool_calls(supervisor_messages),\n", "                \"research_brief\": state.get(\"research_brief\", \"\")\n", "            }\n", "        )\n", "    else:\n", "        return Command(\n", "            goto=next_step,\n", "            update={\n", "                \"supervisor_messages\": tool_messages,\n", "                \"raw_notes\": all_raw_notes\n", "            }\n", "        )\n", "\n", "# ===== GRAPH CONSTRUCTION =====\n", "\n", "# Build supervisor graph\n", "supervisor_builder = StateGraph(SupervisorState)\n", "supervisor_builder.add_node(\"supervisor\", supervisor)\n", "supervisor_builder.add_node(\"supervisor_tools\", supervisor_tools)\n", "supervisor_builder.add_edge(START, \"supervisor\")\n", "supervisor_agent = supervisor_builder.compile()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from deep_research_from_scratch.multi_agent_supervisor import supervisor_agent\n", "\n", "# Show the agent\n", "display(Image(supervisor_agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> I want to identify and evaluate the coffee shops in San Francisco that are considered the best based            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> specifically                                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> on coffee quality. My research should focus on analyzing and comparing coffee shops within the San Francisco    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> area,                                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> using coffee quality as the primary criterion. I am open regarding methods of assessing coffee quality (e.g.,   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> expert reviews, customer ratings, specialty coffee certifications), and there are no constraints on ambiance,   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> location, wifi, or food options unless they directly impact perceived coffee quality. Please prioritize primary <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> sources such as the official websites of coffee shops, reputable third-party coffee review organizations (like  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Coffee Review or Specialty Coffee Association), and prominent review aggregators like Google or Yelp where      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> direct                                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> customer feedback about coffee quality can be found. The study should result in a well-supported list or        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> ranking of                                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> the top coffee shops in San Francisco, emphasizing their coffee quality according to the latest available data  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> as                                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> of July 2025..                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m I want to identify and evaluate the coffee shops in San Francisco that are considered the best based            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m specifically                                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m on coffee quality. My research should focus on analyzing and comparing coffee shops within the San Francisco    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m area,                                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m using coffee quality as the primary criterion. I am open regarding methods of assessing coffee quality (e.g.,   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m expert reviews, customer ratings, specialty coffee certifications), and there are no constraints on ambiance,   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m location, wifi, or food options unless they directly impact perceived coffee quality. Please prioritize primary \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m sources such as the official websites of coffee shops, reputable third-party coffee review organizations (like  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m Coffee Review or Specialty Coffee Association), and prominent review aggregators like Google or Yelp where      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m direct                                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m customer feedback about coffee quality can be found. The study should result in a well-supported list or        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m ranking of                                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m the top coffee shops in San Francisco, emphasizing their coffee quality according to the latest available data  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m as                                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m of July 2025..                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: think_tool                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"reflection\": \"Let me analyze this research request carefully:\\n\\n**User's Core Need**: Identify and evaluate <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> the best coffee shops in San Francisco based specifically on coffee quality, with a focus on creating a         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> well-supported ranking.\\n\\n**Key Requirements**:\\n- Geographic focus: San Francisco area\\n- Primary criterion:  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Coffee quality (not ambiance, location, wifi, food)\\n- Methods: Expert reviews, customer ratings, specialty     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> coffee certifications\\n- Sources: Official websites, Coffee Review, Specialty Coffee Association,               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Google/Yelp\\n- Timeline: Latest data as of July 2025 (though today is August 2025, so this is recent)\\n-        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Output: Well-supported list/ranking\\n\\n**Research Strategy Decision**:\\nThis appears to be a comprehensive but  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> focused research task that can be handled by a single specialized agent. While I could potentially break this   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> into multiple sub-agents (e.g., one for expert reviews, one for customer ratings, one for certifications), the  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> user is asking for an integrated evaluation and ranking that combines all these sources. A single agent can     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> more effectively synthesize these different evaluation methods into a cohesive ranking.\\n\\n**Approach**: I'll   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> delegate this to one research agent with comprehensive instructions to:\\n1. Research top SF coffee shops known  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> for quality\\n2. Evaluate using multiple quality assessment methods\\n3. Prioritize the specified primary         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> sources\\n4. Create a well-supported ranking focused on coffee quality\\n\\nThis is more efficient than            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> fragmenting the research and will ensure better integration of findings.\"                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01X7TdgmGvjBsYT9TuR8p23k                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: think_tool                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"reflection\": \"Let me analyze this research request carefully:\\n\\n**User's Core Need**: Identify and evaluate \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m the best coffee shops in San Francisco based specifically on coffee quality, with a focus on creating a         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m well-supported ranking.\\n\\n**Key Requirements**:\\n- Geographic focus: San Francisco area\\n- Primary criterion:  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Coffee quality (not ambiance, location, wifi, food)\\n- Methods: Expert reviews, customer ratings, specialty     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m coffee certifications\\n- Sources: Official websites, Coffee Review, Specialty Coffee Association,               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Google/Yelp\\n- Timeline: Latest data as of July 2025 (though today is August 2025, so this is recent)\\n-        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Output: Well-supported list/ranking\\n\\n**Research Strategy Decision**:\\nThis appears to be a comprehensive but  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m focused research task that can be handled by a single specialized agent. While I could potentially break this   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m into multiple sub-agents (e.g., one for expert reviews, one for customer ratings, one for certifications), the  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m user is asking for an integrated evaluation and ranking that combines all these sources. A single agent can     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m more effectively synthesize these different evaluation methods into a cohesive ranking.\\n\\n**Approach**: I'll   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m delegate this to one research agent with comprehensive instructions to:\\n1. Research top SF coffee shops known  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m for quality\\n2. Evaluate using multiple quality assessment methods\\n3. Prioritize the specified primary         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m sources\\n4. Create a well-supported ranking focused on coffee quality\\n\\nThis is more efficient than            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m fragmenting the research and will ensure better integration of findings.\"                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01X7TdgmGvjBsYT9TuR8p23k                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Reflection recorded: Let me analyze this research request carefully:                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **User's Core Need**: Identify and evaluate the best coffee shops in San Francisco based specifically on coffee <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> quality, with a focus on creating a well-supported ranking.                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Key Requirements**:                                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Geographic focus: San Francisco area                                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Primary criterion: Coffee quality (not ambiance, location, wifi, food)                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Methods: Expert reviews, customer ratings, specialty coffee certifications                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Sources: Official websites, Coffee Review, Specialty Coffee Association, Google/Yelp                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Timeline: Latest data as of July 2025 (though today is August 2025, so this is recent)                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Output: Well-supported list/ranking                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Research Strategy Decision**:                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> This appears to be a comprehensive but focused research task that can be handled by a single specialized agent. <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> While I could potentially break this into multiple sub-agents (e.g., one for expert reviews, one for customer   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ratings, one for certifications), the user is asking for an integrated evaluation and ranking that combines all <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> these sources. A single agent can more effectively synthesize these different evaluation methods into a         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> cohesive ranking.                                                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Approach**: I'll delegate this to one research agent with comprehensive instructions to:                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. Research top SF coffee shops known for quality                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. Evaluate using multiple quality assessment methods                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 3. Prioritize the specified primary sources                                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 4. Create a well-supported ranking focused on coffee quality                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> This is more efficient than fragmenting the research and will ensure better integration of findings.            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Reflection recorded: Let me analyze this research request carefully:                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **User's Core Need**: Identify and evaluate the best coffee shops in San Francisco based specifically on coffee \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m quality, with a focus on creating a well-supported ranking.                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Key Requirements**:                                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Geographic focus: San Francisco area                                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Primary criterion: Coffee quality (not ambiance, location, wifi, food)                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Methods: Expert reviews, customer ratings, specialty coffee certifications                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Sources: Official websites, Coffee Review, Specialty Coffee Association, Google/Yelp                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Timeline: Latest data as of July 2025 (though today is August 2025, so this is recent)                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Output: Well-supported list/ranking                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Research Strategy Decision**:                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m This appears to be a comprehensive but focused research task that can be handled by a single specialized agent. \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m While I could potentially break this into multiple sub-agents (e.g., one for expert reviews, one for customer   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ratings, one for certifications), the user is asking for an integrated evaluation and ranking that combines all \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m these sources. A single agent can more effectively synthesize these different evaluation methods into a         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m cohesive ranking.                                                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Approach**: I'll delegate this to one research agent with comprehensive instructions to:                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. Research top SF coffee shops known for quality                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. Eva<PERSON>ate using multiple quality assessment methods                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 3. Prioritize the specified primary sources                                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 4. Create a well-supported ranking focused on coffee quality                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m This is more efficient than fragmenting the research and will ensure better integration of findings.            \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: ConductResearch                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"research_topic\": \"Research and evaluate the best coffee shops in San Francisco based on coffee quality.      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Create a comprehensive ranking of top San Francisco coffee shops using coffee quality as the primary criterion. <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Focus on multiple assessment methods including: expert reviews from coffee industry publications, customer      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ratings and feedback specifically about coffee quality, specialty coffee certifications, and roaster            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> credentials. Prioritize primary sources such as official coffee shop websites, reputable third-party coffee     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> review organizations like Coffee Review or Specialty Coffee Association, and prominent review aggregators like  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Google Reviews and Yelp where customers specifically comment on coffee quality. Exclude factors like ambiance,  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> location convenience, wifi, or food options unless they directly impact perceived coffee quality. Provide a     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> well-supported list or ranking with evidence from the latest available data as of July 2025, including specific <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> details about what makes each coffee shop's coffee quality exceptional (such as sourcing practices, roasting    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> methods, brewing techniques, bean quality, or barista expertise).\"                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01MS5u8zNuCvkKuAwcR2wmkX                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: ConductResearch                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"research_topic\": \"Research and evaluate the best coffee shops in San Francisco based on coffee quality.      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Create a comprehensive ranking of top San Francisco coffee shops using coffee quality as the primary criterion. \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Focus on multiple assessment methods including: expert reviews from coffee industry publications, customer      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ratings and feedback specifically about coffee quality, specialty coffee certifications, and roaster            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m credentials. Prioritize primary sources such as official coffee shop websites, reputable third-party coffee     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m review organizations like Coffee Review or Specialty Coffee Association, and prominent review aggregators like  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Google Reviews and Yelp where customers specifically comment on coffee quality. Exclude factors like ambiance,  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m location convenience, wifi, or food options unless they directly impact perceived coffee quality. Provide a     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m well-supported list or ranking with evidence from the latest available data as of July 2025, including specific \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m details about what makes each coffee shop's coffee quality exceptional (such as sourcing practices, roasting    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m methods, brewing techniques, bean quality, or barista expertise).\"                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01MS5u8zNuCvkKuAwcR2wmkX                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **List of Queries and Tool Calls Made**                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. Query: best coffee shops San Francisco 2024 2025 coffee quality specialty coffee roasters expert reviews     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. Query: \"Coffee Review\" \"Specialty Coffee Association\" San Francisco coffee roasters ratings reviews Sextant  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Saint <PERSON>                                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 3. Query: San Francisco coffee roasters expert reviews Coffee Review magazine specialty coffee ratings Blue     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Bottle Ritual Four Barrel                                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 4. Query: Sightglass Coffee Four Barrel Coffee Ritual Coffee San Francisco sourcing roasting methods direct     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> trade specialty coffee quality reviews                                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 5. Query: \"Saint Frank Coffee\" \"Paper Son Coffee\" \"Andytown Coffee\" San Francisco coffee quality barista        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> training sourcing roasting awards 2024 2025                                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ---                                                                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Fully Comprehensive Findings**                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Best Coffee Roaster Near San Francisco, California: Graffeo Coffee Roasting Company. 4.7 (196 reviews);       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Andytown Coffee Roasters. 4.5 (1.1k reviews) [1].                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Eater SF presents a curated list of the absolute best coffee shops in San Francisco as of 2025, highlighting  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> the city’s rich cultural coffee history and innovative coffee scene. San Francisco is noted as a global coffee  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> culture summit, being the birthplace of significant coffee industry changes such as the “waves of coffee”       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> terminology introduced by <PERSON><PERSON> (Wrecking Ball Coffee), and the West Coast introduction of Yemeni       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> coffee by Port of Mokha and Delah Coffee House. The city also claims historic ties to the Irish Coffee and      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Italian espresso cultures and coffee packaging innovations.                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - The list features 16 coffee shops, with recent changes including the removal of Coffee Movement on Balboa     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Street, Poorboy Coffee, and Flywheel Coffee, and additions like Caffe Trieste, the Coffee Movement at Side A,   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> and Cafe Shoji.                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Highlighted shops include:                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Saint <PERSON>: Known for tea-like Bolivian coffee and minimal aesthetic; expanded with new locations   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Juniper and a SoMa venue.                                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Outset Coffee: Offers innovative fruit coffee blends, such as an Ethiopian coffee Americano with orange     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> juice and jasmine green tea.                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Scullery: A small cafe in the Tenderloin serving specialty Mother Tongue Coffee and high-quality toast.     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Sextant Coffee Roasters: Black-owned, sourcing beans directly from growers in Ethiopia, Kenya, and          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Colombia, with pastries from Firebrand Artisan Breads; considered a model of fourth wave coffee.                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Telescope Coffee: SoMa shop known for unique drinks including seasonal honeycomb lattes and a signature     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> strawberry milk served in a strawberry-shaped glass.                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Abanico Coffee Roasters: Mission District shop owned by <PERSON>, originally from El Salvador, brewing     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> specialty drinks inspired by Central American coffee traditions.                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - SPRO: Playful coffee drinks including the Cold Fashioned, combining cold brew with orange bitters and gum   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> syrup; three city locations.                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Grand Coffee: Over 11 years old with two locations; a vibrant spot serving high-quality coffee including    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Chemex brews.                                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Hey Neighbor Cafe: Bayview shop opened in 2021, recognized for quality toast and using beans from Captain + <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Stoker.                                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Excelsior Coffee: Black and brown-owned shop in the Excelsior neighborhood known for its chai and specialty <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> coffee roasts.                                                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Andytown Coffee Roasters: Beloved west side shop reopened with a fresh look and strong Guatemalan espresso  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> shots.                                                                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Hi NRG: A Hong Kong-inspired pop-up known for inventive drinks and lively atmosphere.                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Paper Son: Merges Asian American heritage with skilled espresso and pour-over techniques, newest location   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> opened downtown via the Vacant to Vibrant program.                                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Shoji: SoMa-based cafe with award-winning MAME coffee and a popular spot for matcha-based drinks and        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Japanese cuisine.                                                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - Caffe Trieste: Historic North Beach café famous for dark, sweet coffee and as a cultural landmark linked    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> with filmmaker <PERSON>.                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   - The Coffee Movement at Side A: Collaboration providing coffee, soft serve, and doughnuts, promoting a       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> unique café experience discouraging phone use.                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - San Francisco is and has been for many the world over the summit of coffee culture. It’s the birthplace of    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Wrecking Ball Coffee, where <PERSON><PERSON> introduced the “waves of coffee” terminology and changed the industry <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> forever. These 16 coffee shops keep the torch burning, making those great ascents into the future. Paolo        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> <PERSON><PERSON><PERSON><PERSON> thinks a good coffee shop should offer well-sourced beans, lively conversation, and a respite from    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> the world. Outset on Valencia and New Montgomery streets takes the fruit coffee form to the next level with a   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> delicate, multi-textural drink unlike anything else in the city. Sextant founder <PERSON><PERSON><PERSON> works directly    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> with Ethiopian, Kenyan, and Colombian growers, producing communities owning the means of production [2].        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Coffee Movement and St. Frank are my favs with Sightglass and Ritual being the more “chain” backup (read: you <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> can always find one of…) [3].                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Sextant Coffee Roasters is a specialty coffee company based in San Francisco, with multiple store locations   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> at 1415 Folsom Street, 539 Valencia Street, and 201 3rd Street. They offer top-grade, freshly roasted coffee    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> beans sourced from renowned farms, including a partner farm in Ethiopia, known for washed coffee processing.    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Their product line includes various featured coffee collections such as Maiden Voyage Espresso, Black Lion,     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Dancing <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>'s Horse, showcasing diverse flavor profiles from Ethiopian and Colombian     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> coffees. Alongside coffee, Sextant sells brewing equipment (e.g., French presses, Hario filters, Chemex,        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> AeroPress) and branded merchandise like t-shirts. The company invites customers to subscribe to their artisanal <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> coffee collections and offers a wholesale partnership via an online form. Contact information includes a phone  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> number (************) and email (<EMAIL>). Their website also features a newsletter signup for   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> exclusive updates and events. [6]                                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Sextant Coffee Roasters, San Francisco : See 4 unbiased reviews of Sextant Coffee Roasters, rated 4.5 of 5 on <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Tripadvisor and ranked #2032 of 3345 [4].                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Coffee Review is a leading online guide dedicated to coffee, providing detailed reviews and ratings of        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> coffees and espressos from various roasters, with a focus on those roasted in the greater San Francisco area.   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The site features 260 reviews listed in reverse chronological order, highlighting tasting notes such as aroma,  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> cup profile, acidity, mouthfeel, and finish for each coffee. Examples include Equator Coffees’ Decaf Shakeout   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Blend noted for its sweet nutty and chocolaty character, Sightglass Coffee’s Owl’s <PERSON><PERSON> Espresso Blend praised  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> for its crisply chocolaty and sweet-tart finish, and <PERSON><PERSON><PERSON>'s Coffee &amp; Tea’s Sumatra recognized for its rich      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> earthy sweetness. The website also promotes several top-rated coffee roasters through prominent advertisements  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> and provides services such as advertising opportunities and coffee review submissions. Coffee Review’s detailed <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> tasting notes capture flavors ranging from chocolate, nuts, citrus, spices, and floral tones, supporting        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> consumers in discovering high-quality, expertly evaluated coffee products [7]. Sweetly nut-toned, chocolaty.    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Cocoa powder, roasted almond, pink grapefruit, caramel, nutmeg in aroma and cup; Evaluated as espresso. Crisply <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> chocolaty, sweet and nutty. Baking chocolate, kumquat, cashew, orange zest, fresh oak in aroma and cup;         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Rich-toned, earthy-sweet. Fresh humus, cocoa powder, gently scorched sandalwood, gardenia, graphite in aroma    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> and cup; Rich-toned, complex, bittersweet. Dark chocolate, sweet tobacco, dried fig, nutmeg, molasses in aroma  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> and cup.                                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Four Barrel Coffee (2228 reviews). Sightglass Coffee (2143 reviews). Ritual Coffee Roasters (1980 reviews).   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Blue Bottle Coffee - Hayes Valley Kiosk (1759...) [9].                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Sightglass Coffee is a specialty coffee retailer offering organic and sustainably sourced coffee products,    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> including best sellers like Organic Owl's Howl, Blueboon, Toketee, and seasonal varieties such as Autumnal      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Equinox. They provide various coffee categories including single origin, espresso, blends, instant, and decaf.  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Promotions include 15% off when bundling three bags and free shipping on all 2lb bags. Customers can subscribe  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> for coffee delivery, receive 10% off first orders, and shop goods like mugs and tote bags. Sightglass Coffee    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> operates daily retail locations in San Francisco across SOMA, Mission, and Divisadero districts, and in         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Hollywood, Los Angeles, offering both in-store and to-go orders. The brand emphasizes quality through their     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> roasting, sourcing, and sustainability practices and highlights their presence in reputable publications such   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> as <PERSON>, Chowhound, CNN Underscored, and The New York Times Wirecutter. Bundle 3 Bags and Get 15% Off.   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Free Shipping on all 2lb bags. Autumnal Equinox is back for a well-balanced cup to settle your ritual into the  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> new season. A single origin coffee is a record of a particular time and place. Crafted to display complex,      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> balanced flavors with clarity and rich textures. Carefully sourced coffees, naturally decaffeinated with        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> exceptional flavor and quality. Join us daily in San Francisco or Los Angeles. Subscribers receive first access <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> to special offers and limited releases [10].                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Andytown Coffee Roasters – Downtown, Mission, Outer Richmond, Outer Sunset + SoMa ↓ Must Order Drinks: Snowy  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> <PERSON><PERSON> (Original Bird) [12].                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - San Francisco ’s Best Coffee Shops | Eater SF: The webpage highlights the absolute best coffee shops in San   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Francisco as of 2025, emphasizing the city's rich coffee culture that includes pioneering influences like       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Wrecking Ball Coffee and the introduction of Yemeni coffee on the West Coast. It details changes in notable     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> coffee venues, mentioning that Coffee Movement on Balboa Street, Poorboy Coffee, and Flywheel Coffee were       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> removed from the list, while Caffe Trieste, Coffee Movement at Side A, and Cafe Shoji were added. The guide     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> profiles 16 top coffee shops, each praised for unique offerings and contributions to the city's coffee scene.   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Key featured cafes include Saint Frank Coffee, known for its Bolivian teas and minimalist aesthetic; Outset     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Coffee, recognized for innovative fruit coffee blends; and Sextant Coffee Roasters, which focuses on direct     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> relationships with Ethiopian, Kenyan, and Colombian growers. Additional notable places include Telescope Coffee <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> with its seasonal lattes, Abanico Coffee Roasters bringing Latin American coffee traditions, SPRO for inventive <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> drinks, and historic Caffe Trieste noted for its cultural legacy. The Coffee Movement at Side A merges          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> specialty coffee with soft serve and doughnuts, providing a unique café experience. The piece concludes with    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> author credits and links to related dining guides in San Francisco. San Francisco is and has been for many the  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> world over the summit of coffee culture. These 16 coffee shops keep the torch burning, making those great       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ascents into the future. <PERSON> is to San Francisco coffee as Dungeness crab is to San Francisco           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> restaurants. Outset Americano is a medley of Ethiopian coffee plus not-from-concentrate orange juice, jasmine   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> green tea, and orange syrup. Sextant founder <PERSON><PERSON><PERSON> works directly with Ethiopian, Kenyan, and Colombian  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> growers, making high-end but affordable beans to-go [13].                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ---                                                                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **List of All Relevant Sources (with citations in the report)**                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [1] Best Coffee Roaster San Francisco, CA - Last Updated August 2025 - Yelp:                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://m.yelp.com/search?find_desc=Best+Coffee+Roaster&amp;find_loc=San+Francisco%2C+CA                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [2] The Absolute Best San Francisco Coffee Shops - Eater SF:                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://sf.eater.com/maps/best-coffee-shops-san-francisco                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [3] Recently moved to San Francisco, looking for good coffee place ... - Reddit:                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://www.reddit.com/r/AskSF/comments/1di0g1l/recently_moved_to_san_francisco_looking_for_good/               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [4] Sextant Coffee Roasters - San Francisco Restaurants - Tripadvisor:                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://www.tripadvisor.com/Restaurant_Review-g60713-d15122404-Reviews-Sextant_Coffee_Roasters-San_Francisco_Ca <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> lifornia.html                                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [5] SEXTANT COFFEE ROASTERS - Updated August 2025 - Yelp:                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://www.yelp.com/biz/sextant-coffee-roasters-san-francisco                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [6] Sextant Coffee Roasters - Best Speciality Coffee in San ...:                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://sextantcoffee.com/?srsltid=AfmBOor5HMUq7Ou7XCwlSdVtZdhrU6Z0-Mr052Yunq0owT2VAvA9m2vd                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [7] San Francisco Coffee Ratings and Reviews - Coffee Review:                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://www.coffeereview.com/best-coffee-cities/san-francisco/                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [8] Blue Bottle Coffee | Fresh Roasted Specialty Coffee:                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://bluebottlecoffee.com/?srsltid=AfmBOordS_-ygbQq5WKaZsn7h8r_xKqWXtKq30zLayttsMysBNse0cEA                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [9] Blue Bottle Coffe San Francisco, CA - Last Updated August 2025 - Yelp:                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://www.yelp.com/search?find_desc=Blue+<PERSON><PERSON>+<PERSON><PERSON>&amp;find_loc=San+Francisco%2C+CA                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [10] Sightglass Coffee:                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> https://sightglasscoffee.com/?srsltid=AfmBOop1VARlyNB5tmvFKhGVfTUQjJZ3yq2OocJ9hYxnTy-7cy909KJq                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [11] SIGHTGLASS COFFEE - Updated August 2025 - Yelp: https://www.yelp.com/biz/sightglass-coffee-san-francisco-7 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [12] San Francisco Coffee Worth Seeking - Food GPS: https://foodgps.com/san-francisco-coffee-worth-seeking/     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> [13] San Francisco’s Best Coffee Shops | Eater SF: https://sf.eater.com/maps/best-coffee-shops-san-francisco    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m **List of Queries and Tool Calls Made**                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. Query: best coffee shops San Francisco 2024 2025 coffee quality specialty coffee roasters expert reviews     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. Query: \"Coffee Review\" \"Specialty Coffee Association\" San Francisco coffee roasters ratings reviews Sextant  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m <PERSON>                                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 3. Query: San Francisco coffee roasters expert reviews Coffee Review magazine specialty coffee ratings Blue     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Bottle Ritual Four Barrel                                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 4. Query: Sightglass Coffee Four Barrel Coffee Ritual Coffee San Francisco sourcing roasting methods direct     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m trade specialty coffee quality reviews                                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 5. Query: \"Saint Frank Coffee\" \"Paper Son Coffee\" \"Andytown Coffee\" San Francisco coffee quality barista        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m training sourcing roasting awards 2024 2025                                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ---                                                                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Fully Comprehensive Findings**                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Best Coffee Roaster Near San Francisco, California: Graffeo Coffee Roasting Company. 4.7 (196 reviews);       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Andytown Coffee Roasters. 4.5 (1.1k reviews) [1].                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Eater SF presents a curated list of the absolute best coffee shops in San Francisco as of 2025, highlighting  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m the city’s rich cultural coffee history and innovative coffee scene. San Francisco is noted as a global coffee  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m culture summit, being the birthplace of significant coffee industry changes such as the “waves of coffee”       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m terminology introduced by <PERSON><PERSON> (Wrecking Ball Coffee), and the West Coast introduction of Yemeni       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m coffee by Port of Mokha and Delah Coffee House. The city also claims historic ties to the Irish Coffee and      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Italian espresso cultures and coffee packaging innovations.                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - The list features 16 coffee shops, with recent changes including the removal of Coffee Movement on Balboa     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Street, Poorboy Coffee, and Flywheel Coffee, and additions like Caffe Trieste, the Coffee Movement at Side A,   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m and Cafe Shoji.                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Highlighted shops include:                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Saint Frank Coffee: Known for tea-like Bolivian coffee and minimal aesthetic; expanded with new locations   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Juniper and a SoMa venue.                                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Outset Coffee: Offers innovative fruit coffee blends, such as an Ethiopian coffee Americano with orange     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m juice and jasmine green tea.                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Scullery: A small cafe in the Tenderloin serving specialty Mother Tongue Coffee and high-quality toast.     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Sextant Coffee Roasters: Black-owned, sourcing beans directly from growers in Ethiopia, Kenya, and          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Colombia, with pastries from Firebrand Artisan Breads; considered a model of fourth wave coffee.                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Telescope Coffee: SoMa shop known for unique drinks including seasonal honeycomb lattes and a signature     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m strawberry milk served in a strawberry-shaped glass.                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Abanico Coffee Roasters: Mission District shop owned by <PERSON> Valle, originally from El Salvador, brewing     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m specialty drinks inspired by Central American coffee traditions.                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - SPRO: Playful coffee drinks including the Cold Fashioned, combining cold brew with orange bitters and gum   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m syrup; three city locations.                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Grand Coffee: Over 11 years old with two locations; a vibrant spot serving high-quality coffee including    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Chemex brews.                                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Hey Neighbor Cafe: Bayview shop opened in 2021, recognized for quality toast and using beans from Captain + \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m <PERSON><PERSON>.                                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Excelsior Coffee: Black and brown-owned shop in the Excelsior neighborhood known for its chai and specialty \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m coffee roasts.                                                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Andytown Coffee Roasters: Beloved west side shop reopened with a fresh look and strong Guatemalan espresso  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m shots.                                                                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Hi NRG: A Hong Kong-inspired pop-up known for inventive drinks and lively atmosphere.                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Paper Son: Merges Asian American heritage with skilled espresso and pour-over techniques, newest location   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m opened downtown via the Vacant to Vibrant program.                                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - Shoji: SoMa-based cafe with award-winning MAME coffee and a popular spot for matcha-based drinks and        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Japanese cuisine.                                                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - C<PERSON>fe Trieste: Historic North Beach café famous for dark, sweet coffee and as a cultural landmark linked    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m with filmmaker <PERSON>.                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   - The Coffee Movement at Side A: Collaboration providing coffee, soft serve, and doughnuts, promoting a       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m unique café experience discouraging phone use.                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - San Francisco is and has been for many the world over the summit of coffee culture. It’s the birthplace of    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Wrecking Ball Coffee, where <PERSON><PERSON> introduced the “waves of coffee” terminology and changed the industry \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m forever. These 16 coffee shops keep the torch burning, making those great ascents into the future. <PERSON>        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m <PERSON><PERSON><PERSON><PERSON> thinks a good coffee shop should offer well-sourced beans, lively conversation, and a respite from    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m the world. Outset on Valencia and New Montgomery streets takes the fruit coffee form to the next level with a   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m delicate, multi-textural drink unlike anything else in the city. Sextant founder <PERSON><PERSON><PERSON> works directly    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m with Ethiopian, Kenyan, and Colombian growers, producing communities owning the means of production [2].        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Coffee Movement and St. Frank are my favs with Sightglass and Ritual being the more “chain” backup (read: you \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m can always find one of…) [3].                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Sextant Coffee Roasters is a specialty coffee company based in San Francisco, with multiple store locations   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m at 1415 Folsom Street, 539 Valencia Street, and 201 3rd Street. They offer top-grade, freshly roasted coffee    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m beans sourced from renowned farms, including a partner farm in Ethiopia, known for washed coffee processing.    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Their product line includes various featured coffee collections such as Maiden Voyage Espresso, Black Lion,     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>'s Horse, showcasing diverse flavor profiles from Ethiopian and Colombian     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m coffees. Alongside coffee, Sextant sells brewing equipment (e.g., French presses, Hario filters, Chemex,        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m AeroPress) and branded merchandise like t-shirts. The company invites customers to subscribe to their artisanal \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m coffee collections and offers a wholesale partnership via an online form. Contact information includes a phone  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m number (************) and email (<EMAIL>). Their website also features a newsletter signup for   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m exclusive updates and events. [6]                                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Sextant Coffee Roasters, San Francisco : See 4 unbiased reviews of Sextant Coffee Roasters, rated 4.5 of 5 on \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Tripadvisor and ranked #2032 of 3345 [4].                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Coffee Review is a leading online guide dedicated to coffee, providing detailed reviews and ratings of        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m coffees and espressos from various roasters, with a focus on those roasted in the greater San Francisco area.   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The site features 260 reviews listed in reverse chronological order, highlighting tasting notes such as aroma,  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m cup profile, acidity, mouthfeel, and finish for each coffee. Examples include Equator Coffees’ Decaf Shakeout   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Blend noted for its sweet nutty and chocolaty character, Sightglass Coffee’s Owl’s Howl Espresso Blend praised  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m for its crisply chocolaty and sweet-tart finish, and Peet's Coffee & Tea’s Sumatra recognized for its rich      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m earthy sweetness. The website also promotes several top-rated coffee roasters through prominent advertisements  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m and provides services such as advertising opportunities and coffee review submissions. Coffee Review’s detailed \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m tasting notes capture flavors ranging from chocolate, nuts, citrus, spices, and floral tones, supporting        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m consumers in discovering high-quality, expertly evaluated coffee products [7]. Sweetly nut-toned, chocolaty.    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Cocoa powder, roasted almond, pink grapefruit, caramel, nutmeg in aroma and cup; Evaluated as espresso. Crisply \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m chocolaty, sweet and nutty. Baking chocolate, kumquat, cashew, orange zest, fresh oak in aroma and cup;         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Rich-toned, earthy-sweet. Fresh humus, cocoa powder, gently scorched sandalwood, gardenia, graphite in aroma    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m and cup; Rich-toned, complex, bittersweet. Dark chocolate, sweet tobacco, dried fig, nutmeg, molasses in aroma  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m and cup.                                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Four Barrel Coffee (2228 reviews). Sightglass Coffee (2143 reviews). Ritual Coffee Roasters (1980 reviews).   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Blue Bottle Coffee - Hayes Valley Kiosk (1759...) [9].                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Sightglass Coffee is a specialty coffee retailer offering organic and sustainably sourced coffee products,    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m including best sellers like Organic Owl's Howl, Blueboon, Toketee, and seasonal varieties such as Autumnal      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Equinox. They provide various coffee categories including single origin, espresso, blends, instant, and decaf.  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Promotions include 15% off when bundling three bags and free shipping on all 2lb bags. Customers can subscribe  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m for coffee delivery, receive 10% off first orders, and shop goods like mugs and tote bags. Sightglass Coffee    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m operates daily retail locations in San Francisco across SOMA, Mission, and Divisadero districts, and in         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Hollywood, Los Angeles, offering both in-store and to-go orders. The brand emphasizes quality through their     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m roasting, sourcing, and sustainability practices and highlights their presence in reputable publications such   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m as <PERSON>, Chowhound, CNN Underscored, and The New York Times Wirecutter. Bundle 3 Bags and Get 15% Off.   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Free Shipping on all 2lb bags. Autumnal Equinox is back for a well-balanced cup to settle your ritual into the  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m new season. A single origin coffee is a record of a particular time and place. Crafted to display complex,      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m balanced flavors with clarity and rich textures. Carefully sourced coffees, naturally decaffeinated with        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m exceptional flavor and quality. Join us daily in San Francisco or Los Angeles. Subscribers receive first access \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m to special offers and limited releases [10].                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Andytown Coffee Roasters – Downtown, Mission, Outer Richmond, Outer Sunset + SoMa ↓ Must Order Drinks: <PERSON>y  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m <PERSON><PERSON> (<PERSON> <PERSON>) [12].                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - San Francisco ’s Best Coffee Shops | Eater SF: The webpage highlights the absolute best coffee shops in San   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Francisco as of 2025, emphasizing the city's rich coffee culture that includes pioneering influences like       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Wrecking Ball Coffee and the introduction of Yemeni coffee on the West Coast. It details changes in notable     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m coffee venues, mentioning that Coffee Movement on Balboa Street, Poorboy Coffee, and Flywheel Coffee were       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m removed from the list, while Caffe Trieste, Coffee Movement at Side A, and Cafe Shoji were added. The guide     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m profiles 16 top coffee shops, each praised for unique offerings and contributions to the city's coffee scene.   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Key featured cafes include Saint Frank Coffee, known for its Bolivian teas and minimalist aesthetic; Outset     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Coffee, recognized for innovative fruit coffee blends; and Sextant Coffee Roasters, which focuses on direct     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m relationships with Ethiopian, Kenyan, and Colombian growers. Additional notable places include Telescope Coffee \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m with its seasonal lattes, Abanico Coffee Roasters bringing Latin American coffee traditions, SPRO for inventive \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m drinks, and historic Caffe Trieste noted for its cultural legacy. The Coffee Movement at Side A merges          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m specialty coffee with soft serve and doughnuts, providing a unique café experience. The piece concludes with    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m author credits and links to related dining guides in San Francisco. San Francisco is and has been for many the  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m world over the summit of coffee culture. These 16 coffee shops keep the torch burning, making those great       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ascents into the future. <PERSON> is to San Francisco coffee as Dungeness crab is to San Francisco           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m restaurants. Outset Americano is a medley of Ethiopian coffee plus not-from-concentrate orange juice, jasmine   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m green tea, and orange syrup. Sextant founder <PERSON><PERSON><PERSON> works directly with Ethiopian, Kenyan, and Colombian  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m growers, making high-end but affordable beans to-go [13].                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ---                                                                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **List of All Relevant Sources (with citations in the report)**                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [1] Best Coffee Roaster San Francisco, CA - Last Updated August 2025 - Yelp:                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://m.yelp.com/search?find_desc=Best+Coffee+Roaster&find_loc=San+Francisco%2C+CA                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [2] The Absolute Best San Francisco Coffee Shops - Eater SF:                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://sf.eater.com/maps/best-coffee-shops-san-francisco                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [3] Recently moved to San Francisco, looking for good coffee place ... - Reddit:                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://www.reddit.com/r/AskSF/comments/1di0g1l/recently_moved_to_san_francisco_looking_for_good/               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [4] Sextant Coffee Roasters - San Francisco Restaurants - Tripadvisor:                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://www.tripadvisor.com/Restaurant_Review-g60713-d15122404-Reviews-Sextant_Coffee_Roasters-San_Francisco_Ca \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m lifornia.html                                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [5] SEXTANT COFFEE ROASTERS - Updated August 2025 - Yelp:                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://www.yelp.com/biz/sextant-coffee-roasters-san-francisco                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [6] Sextant Coffee Roasters - Best Speciality Coffee in San ...:                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://sextantcoffee.com/?srsltid=AfmBOor5HMUq7Ou7XCwlSdVtZdhrU6Z0-Mr052Yunq0owT2VAvA9m2vd                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [7] San Francisco Coffee Ratings and Reviews - Coffee Review:                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://www.coffeereview.com/best-coffee-cities/san-francisco/                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [8] Blue Bottle Coffee | Fresh Roasted Specialty Coffee:                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://bluebottlecoffee.com/?srsltid=AfmBOordS_-ygbQq5WKaZsn7h8r_xKqWXtKq30zLayttsMysBNse0cEA                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [9] Blue Bottle Coffe San Francisco, CA - Last Updated August 2025 - Yelp:                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://www.yelp.com/search?find_desc=Blue+<PERSON><PERSON>+<PERSON><PERSON>&find_loc=San+Francisco%2C+CA                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [10] Sightglass Coffee:                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m https://sightglasscoffee.com/?srsltid=AfmBOop1VARlyNB5tmvFKhGVfTUQjJZ3yq2OocJ9hYxnTy-7cy909KJq                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [11] SIGHTGLASS COFFEE - Updated August 2025 - Yelp: https://www.yelp.com/biz/sightglass-coffee-san-francisco-7 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [12] San Francisco Coffee Worth Seeking - Food GPS: https://foodgps.com/san-francisco-coffee-worth-seeking/     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m [13] San Francisco’s Best Coffee Shops | Eater SF: https://sf.eater.com/maps/best-coffee-shops-san-francisco    \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: think_tool                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"reflection\": \"Excellent! The research agent has provided comprehensive findings about San Francisco's top    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> coffee shops based on coffee quality. Let me analyze what we've gathered:\\n\\n**Key Information Found**:\\n1.     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Authoritative Sources**: The research successfully accessed Eater SF (major food publication), Coffee Review  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> (specialized coffee evaluation site), Yelp reviews, and official coffee shop websites.\\n\\n2. **Comprehensive    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Coverage**: The research identified 16+ top coffee shops with detailed information about what makes their       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> coffee quality exceptional.\\n\\n3. **Quality Assessment Methods**: The findings include:\\n   - Expert reviews    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> from Coffee Review with detailed tasting notes\\n   - Customer ratings and review counts from Yelp\\n   -         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Specialty coffee credentials (direct trade, sourcing practices)\\n   - Recognition from major publications       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> (Eater SF's curated list)\\n\\n4. **Specific Quality Details**: For many shops, we have concrete details about:\\n <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Sourcing practices (direct trade relationships)\\n   - Roasting methods and expertise\\n   - Specific coffee    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> origins and processing methods\\n   - Unique brewing techniques and specialty drinks\\n   - Awards and industry   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> recognition\\n\\n**Quality of Evidence**: The research provides well-supported evidence from:\\n- Coffee Review    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> (industry authority) with specific ratings and tasting notes\\n- Eater SF's 2025 curated list (major food        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> publication)\\n- Yelp ratings with review counts\\n- Official websites showing sourcing and roasting              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> credentials\\n\\n**Completeness**: The research covers the full spectrum of what the user requested:\\n\\u2713      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Geographic focus: San Francisco area\\n\\u2713 Primary criterion: Coffee quality with specific details\\n\\u2713    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Multiple assessment methods: Expert reviews, customer ratings, specialty certifications\\n\\u2713 Prioritized     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> primary sources: Official websites, Coffee Review, review aggregators\\n\\u2713 Latest data: 2025                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> information\\n\\u2713 Well-supported ranking with evidence\\n\\n**Assessment**: This research is comprehensive and  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> provides exactly what the user requested. We have enough information to create a well-supported ranking of San  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Francisco's top coffee shops based on coffee quality, with specific evidence for each recommendation. The       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> findings include both broad recognition (Eater SF's authoritative list) and detailed quality assessments        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> (Coffee Review's expert evaluations).\\n\\n**Decision**: I have sufficient information to answer the user's       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> question comprehensively. The research covers all required elements and provides the evidence-based ranking     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> they requested.\"                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01Jv53CjxQgMhvqCsUJV9c3D                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: think_tool                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"reflection\": \"Excellent! The research agent has provided comprehensive findings about San Francisco's top    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m coffee shops based on coffee quality. Let me analyze what we've gathered:\\n\\n**Key Information Found**:\\n1.     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Authoritative Sources**: The research successfully accessed Eater SF (major food publication), Coffee Review  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m (specialized coffee evaluation site), Yelp reviews, and official coffee shop websites.\\n\\n2. **Comprehensive    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Coverage**: The research identified 16+ top coffee shops with detailed information about what makes their       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m coffee quality exceptional.\\n\\n3. **Quality Assessment Methods**: The findings include:\\n   - Expert reviews    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m from Coffee Review with detailed tasting notes\\n   - Customer ratings and review counts from Yelp\\n   -         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Specialty coffee credentials (direct trade, sourcing practices)\\n   - Recognition from major publications       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m (Eater SF's curated list)\\n\\n4. **Specific Quality Details**: For many shops, we have concrete details about:\\n \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Sourcing practices (direct trade relationships)\\n   - Roasting methods and expertise\\n   - Specific coffee    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m origins and processing methods\\n   - Unique brewing techniques and specialty drinks\\n   - Awards and industry   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m recognition\\n\\n**Quality of Evidence**: The research provides well-supported evidence from:\\n- Coffee Review    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m (industry authority) with specific ratings and tasting notes\\n- Eater SF's 2025 curated list (major food        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m publication)\\n- Yelp ratings with review counts\\n- Official websites showing sourcing and roasting              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m credentials\\n\\n**Completeness**: The research covers the full spectrum of what the user requested:\\n\\u2713      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Geographic focus: San Francisco area\\n\\u2713 Primary criterion: Coffee quality with specific details\\n\\u2713    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Multiple assessment methods: Expert reviews, customer ratings, specialty certifications\\n\\u2713 Prioritized     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m primary sources: Official websites, Coffee Review, review aggregators\\n\\u2713 Latest data: 2025                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m information\\n\\u2713 Well-supported ranking with evidence\\n\\n**Assessment**: This research is comprehensive and  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m provides exactly what the user requested. We have enough information to create a well-supported ranking of San  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Francisco's top coffee shops based on coffee quality, with specific evidence for each recommendation. The       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m findings include both broad recognition (Eater SF's authoritative list) and detailed quality assessments        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m (Coffee Review's expert evaluations).\\n\\n**Decision**: I have sufficient information to answer the user's       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m question comprehensively. The research covers all required elements and provides the evidence-based ranking     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m they requested.\"                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01Jv53CjxQgMhvqCsUJV9c3D                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Run the multi-agent supervisor agent\n", "from utils import format_messages\n", "from langchain_core.messages import HumanMessage\n", "\n", "research_brief = \"\"\"I want to identify and evaluate the coffee shops in San Francisco that are considered the best based specifically  \n", "on coffee quality. My research should focus on analyzing and comparing coffee shops within the San Francisco area, \n", "using coffee quality as the primary criterion. I am open regarding methods of assessing coffee quality (e.g.,      \n", "expert reviews, customer ratings, specialty coffee certifications), and there are no constraints on ambiance,      \n", "location, wifi, or food options unless they directly impact perceived coffee quality. Please prioritize primary    \n", "sources such as the official websites of coffee shops, reputable third-party coffee review organizations (like     \n", "Coffee Review or Specialty Coffee Association), and prominent review aggregators like Google or Yelp where direct  \n", "customer feedback about coffee quality can be found. The study should result in a well-supported list or ranking of\n", "the top coffee shops in San Francisco, emphasizing their coffee quality according to the latest available data as  \n", "of July 2025.\"\"\"\n", "\n", "result = await supervisor_agent.ainvoke({\"supervisor_messages\": [HumanMessage(content=f\"{research_brief}.\")]})\n", "format_messages(result['supervisor_messages'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can look at the trace [here](https://smith.langchain.com/public/99eaaecd-fd9a-4ead-8eed-abdfe4ab9288/r).\n", "\n", "### LangGraph Studio\n", "\n", "Just as we did before, we can also use LangGraph Studio to visualize the agent. \n", "\n", "This agent has been added to the `langgraph.json` file, so you can select `research_agent_supervisor` in the dropdown menu:\n", "\n", "```\n", "\"research_agent_supervisor\": \"./src/deep_research_from_scratch/multi_agent_supervisor.py:supervisor_agent\",\n", "```\n", "\n", "Run the following command to start the studio\n", "\n", "```bash\n", "uvx --refresh --from \"langgraph-cli[inmem]\" --with-editable . --python 3.11 langgraph dev --allow-blocking\n", "```\n", "\n", "## <PERSON>l\n", "\n", "The clear benefit of multi-agent is context isolation when there are clearly separable sub-topics. As we discuss [in our blog](https://blog.langchain.com/open-deep-research/): \n", "\n", "> Our experiments showed that single agent response quality suffers if the request has multiple sub-topics (e.g., compare A to B to C). The intuition here is straightforward: a single context window needs to store and reason about tool feedback across all of the sub-topics. This tool feedback is often token heavy. Numerous failure modes, such as context clash, become prevalent as the context window accumulates tool calls across many different sub-topics.\n", "\n", "But, we want to be careful not to spawn sub-agents in cases where it is not required:\n", "\n", "> The supervisor can handle both cases by selectively spawning sub-agents to tune the level of research depth needed for a request. The supervisor is prompted with heuristics to reason about when research should be parallelized, and when a single thread of research is sufficient. Our deep research agent has the flexibility to choose whether to parallelize research or not.\n", "\n", "Let's set up a few tests to make sure that we are parallelizing when it makes sense."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Example messages with think_tool calls and tool messages for evaluation\n", "from langchain_core.messages import HumanMessage, AIMessage, ToolMessage\n", "\n", "should_parallelize = [\n", "    HumanMessage(content=\"Compare OpenAI vs Gemini deep research.\"),\n", "    AIMessage(content=\"I need to analyze this request to determine if can should be parallelized.\", tool_calls=[\n", "        {\n", "            \"name\": \"think_tool\",\n", "            \"args\": {\"reflection\": \"This is a comparison task involving two distinct AI products: OpenAI v Gemini Deep Research.\"},\n", "            \"id\": \"call_think_1\"\n", "        }\n", "    ]),\n", "    ToolMessage(content=\"Analysis complete: This is a comparison task involving two distinct AI products: OpenAI v Gemini Deep Research.\", tool_call_id=\"call_think_1\", name=\"think_tool\")\n", "]\n", "\n", "should_not_parallelize = [\n", "    HumanMessage(content=\"What are the top three Chinese restaurants in Chelsea, Manhattan\"),\n", "    AIMessage(content=\"Let me think about whether this task requires parallelization.\", tool_calls=[\n", "        {\n", "            \"name\": \"think_tool\", \n", "            \"args\": {\"reflection\": \"This is a ranking/listing task for restaurants in a specific geographic area (Chelsea, Manhattan).\"},\n", "            \"id\": \"call_think_2\"\n", "        }\n", "    ]),\n", "    ToolMessage(content=\"Analysis complete: This is a ranking/listing task for restaurants in a specific geographic area (Chelsea, Manhattan).\", tool_call_id=\"call_think_2\", name=\"think_tool\")\n", "]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import os\n", "from langsmith import Client\n", "\n", "# Initialize client \n", "langsmith_client = Client(api_key=os.getenv(\"LANGSMITH_API_KEY\"))\n", "\n", "# Create the dataset\n", "dataset_name = \"deep_research_supervisor_parallelism\"\n", "if not langsmith_client.has_dataset(dataset_name=dataset_name):\n", "\n", "    dataset = langsmith_client.create_dataset(\n", "        dataset_name=dataset_name,\n", "        description=\"A dataset that evaluates whether a supervisor can accurately decide when to parallelize research.\",\n", "    )\n", "\n", "    langsmith_client.create_examples(\n", "        dataset_id=dataset.id,\n", "        examples=[\n", "            {\n", "                \"inputs\": {\"supervisor_messages\": should_parallelize},\n", "                \"outputs\": {\"num_expected_threads\": 2},\n", "            },\n", "            {\n", "                \"inputs\": {\"supervisor_messages\": should_not_parallelize},\n", "                \"outputs\": {\"num_expected_threads\": 1},\n", "            },\n", "        ],\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can run evals."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["View the evaluation results for experiment: 'Supervisor Parallelism-b4e2c6e6' at:\n", "https://smith.langchain.com/o/ebbaf2eb-769b-4505-aca2-d11de10372a4/datasets/87d6f859-3fc2-4727-a213-4106ef8851ae/compare?selectedSessions=a7113523-bfbb-472b-bf37-810ae9f22e09\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ccaecc12ed12481ba85654753d00cb07", "version_major": 2, "version_minor": 0}, "text/plain": ["0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<AsyncExperimentResults Supervisor Parallelism-b4e2c6e6>"], "text/plain": ["<AsyncExperimentResults Supervisor Parallelism-b4e2c6e6>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import uuid\n", "\n", "def evaluate_parallelism(outputs: dict, reference_outputs:dict):\n", "    tool_calls = outputs[\"output\"].update[\"supervisor_messages\"][-1].tool_calls\n", "    return {\n", "        \"key\": \"correct_next_step\",\n", "        \"score\": len(tool_calls) == reference_outputs[\"num_expected_threads\"]\n", "    }\n", "\n", "async def target_func(inputs: dict):\n", "    config = {\"configurable\": {\"thread_id\": uuid.uuid4()}}\n", "    return await supervisor_agent.nodes[\"supervisor\"].ainvoke(inputs, config=config)\n", "\n", "await langsmith_client.aevaluate(\n", "    target_func,\n", "    data=dataset_name,\n", "    evaluators=[evaluate_parallelism],\n", "    experiment_prefix=\"Supervisor Parallelism\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the first example, we asked the agent to compare and contrast products.\n", "\n", "This sort of problem is great for parallel research, and we expect the agent to deep dive into each lab in parallel before synthesizing findings when writing the report.\n", "\n", "The second example asks the agent for the top restaurants in Chelsea, Manhattan. \n", "\n", "The agent needs to conduct a single thread of research here and reflect on websites that presumably list out highly rated restaurants. \n", "\n", "There's not an obvious opportunity for parallelism for this ranking task."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 4}